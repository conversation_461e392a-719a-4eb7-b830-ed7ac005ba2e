cmake_minimum_required(VERSION 3.12)
project(BSPMLRealtime VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Find required packages
find_package(Threads REQUIRED)

# Optional: Find Eigen for linear algebra (commonly used in signal processing)
find_package(Eigen3 QUIET)
if(Eigen3_FOUND)
    message(STATUS "Found Eigen3: ${EIGEN3_INCLUDE_DIR}")
    add_definitions(-DHAVE_EIGEN3)
endif()

# Optional: Find FFTW for frequency domain processing
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(FFTW3 fftw3)
    if(FFTW3_FOUND)
        message(STATUS "Found FFTW3")
        add_definitions(-DHAVE_FFTW3)
    endif()
endif()

# Source files
set(BSPML_SOURCES
    src/preprocessing/detrending.cpp
    src/preprocessing/denoising.cpp
    src/preprocessing/motion_artifacts.cpp
    src/preprocessing/pipeline.cpp
    src/hr_estimation/peak_detection.cpp
    src/hr_estimation/hr_calculation.cpp
    src/hr_estimation/pipeline.cpp
    src/realtime/realtime_processor.cpp
    src/realtime/circular_buffer.cpp
    src/utils/signal_utils.cpp
    src/utils/math_utils.cpp
)

# Header files
set(BSPML_HEADERS
    include/bspml/preprocessing/detrending.h
    include/bspml/preprocessing/denoising.h
    include/bspml/preprocessing/motion_artifacts.h
    include/bspml/preprocessing/pipeline.h
    include/bspml/hr_estimation/peak_detection.h
    include/bspml/hr_estimation/hr_calculation.h
    include/bspml/hr_estimation/pipeline.h
    include/bspml/realtime/realtime_processor.h
    include/bspml/realtime/circular_buffer.h
    include/bspml/utils/signal_utils.h
    include/bspml/utils/math_utils.h
    include/bspml/bspml.h
)

# Create static library
add_library(bspml_static STATIC ${BSPML_SOURCES})
target_include_directories(bspml_static PUBLIC 
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Create shared library
add_library(bspml_shared SHARED ${BSPML_SOURCES})
target_include_directories(bspml_shared PUBLIC 
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# Set library properties
set_target_properties(bspml_static PROPERTIES OUTPUT_NAME bspml)
set_target_properties(bspml_shared PROPERTIES OUTPUT_NAME bspml)

# Link libraries
target_link_libraries(bspml_static Threads::Threads)
target_link_libraries(bspml_shared Threads::Threads)

if(Eigen3_FOUND)
    target_link_libraries(bspml_static Eigen3::Eigen)
    target_link_libraries(bspml_shared Eigen3::Eigen)
endif()

if(FFTW3_FOUND)
    target_link_libraries(bspml_static ${FFTW3_LIBRARIES})
    target_link_libraries(bspml_shared ${FFTW3_LIBRARIES})
    target_include_directories(bspml_static PRIVATE ${FFTW3_INCLUDE_DIRS})
    target_include_directories(bspml_shared PRIVATE ${FFTW3_INCLUDE_DIRS})
endif()

# Examples
add_subdirectory(examples)

# Tests
option(BUILD_TESTS "Build tests" ON)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Installation
install(TARGETS bspml_static bspml_shared
    EXPORT BSPMLTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(DIRECTORY include/ DESTINATION include)

install(EXPORT BSPMLTargets
    FILE BSPMLTargets.cmake
    NAMESPACE BSPML::
    DESTINATION lib/cmake/BSPML
)

# Create config file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    BSPMLConfigVersion.cmake
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/cmake/BSPMLConfig.cmake.in
    ${CMAKE_CURRENT_BINARY_DIR}/BSPMLConfig.cmake
    INSTALL_DESTINATION lib/cmake/BSPML
)

install(FILES
    ${CMAKE_CURRENT_BINARY_DIR}/BSPMLConfig.cmake
    ${CMAKE_CURRENT_BINARY_DIR}/BSPMLConfigVersion.cmake
    DESTINATION lib/cmake/BSPML
)
