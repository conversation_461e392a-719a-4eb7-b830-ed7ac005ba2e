# BSPML C++ Real-time Implementation

This directory contains the C++ implementation of the BSPML (Biosignal Processing and Machine Learning) library for real-time PPG signal processing and heart rate estimation.

## Overview

The C++ implementation is designed for deployment on mobile devices and embedded systems where real-time performance is critical. It provides the same signal processing algorithms as the Python version but optimized for low-latency, continuous processing.

## Current Status

**⚠️ PLACEHOLDER IMPLEMENTATION ⚠️**

This is currently a placeholder implementation with the following structure in place:
- Complete header files with full API definitions
- CMake build system configuration
- Basic implementation stubs
- Example applications
- Test framework setup

The full implementation will be completed after the Python algorithms are finalized and validated.

## Architecture

```
cpp_realtime/
├── include/bspml/          # Public header files
│   ├── preprocessing/      # Signal preprocessing headers
│   ├── hr_estimation/      # Heart rate estimation headers
│   ├── realtime/          # Real-time processing headers
│   ├── utils/             # Utility headers
│   └── bspml.h            # Main library header
├── src/                   # Implementation files
│   ├── preprocessing/     # Preprocessing implementations
│   ├── hr_estimation/     # HR estimation implementations
│   ├── realtime/          # Real-time processing implementations
│   └── utils/             # Utility implementations
├── examples/              # Example applications
├── tests/                 # Unit tests
└── CMakeLists.txt         # Build configuration
```

## Features (Planned)

### Signal Preprocessing
- **Wavelet-based detrending**: Remove baseline drift
- **Band-pass filtering**: Eliminate noise outside 0.5-4 Hz range
- **Motion artifact removal**: RLS adaptive filtering with accelerometer reference
- **Real-time processing**: Optimized for continuous data streams

### Heart Rate Estimation
- **Peak detection**: Local maxima detection in sliding windows
- **Instantaneous HR**: Real-time heart rate calculation
- **Quality assessment**: Signal quality monitoring
- **Confidence estimation**: Reliability metrics for HR estimates

### Real-time Processing
- **Circular buffers**: Efficient data management for continuous streams
- **Multi-threading**: Separate processing and I/O threads
- **Low latency**: Optimized for minimal processing delay
- **Memory efficient**: Fixed memory allocation for embedded systems

## Building

### Prerequisites
- CMake 3.12 or higher
- C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2017+)
- Optional: Eigen3 for linear algebra operations
- Optional: FFTW3 for frequency domain processing

### Build Instructions

```bash
cd cpp_realtime
mkdir build && cd build
cmake ..
make -j$(nproc)
```

### Build Options
```bash
# Release build (optimized)
cmake -DCMAKE_BUILD_TYPE=Release ..

# Debug build
cmake -DCMAKE_BUILD_TYPE=Debug ..

# Disable tests
cmake -DBUILD_TESTS=OFF ..
```

## Usage

### Basic Usage
```cpp
#include "bspml/bspml.h"

// Initialize library
bspml::initialize();

// Create processor
bspml::realtime::RealtimeConfig config;
config.sampling_rate = 64.0;
bspml::realtime::RealtimeProcessor processor(config);

// Process samples
double hr = processor.processSample(ppg_sample, acc_x, acc_y, acc_z);

// Cleanup
bspml::cleanup();
```

### Real-time Processing with Callbacks
```cpp
class MyCallback : public bspml::realtime::HeartRateCallback {
public:
    void onHeartRateUpdate(const bspml::realtime::RealtimeResult& result) override {
        if (result.valid) {
            std::cout << "HR: " << result.heart_rate_bpm << " BPM" << std::endl;
        }
    }
    
    void onSignalQualityChange(double quality) override {
        std::cout << "Quality: " << quality << std::endl;
    }
};

auto callback = std::make_shared<MyCallback>();
bspml::realtime::CallbackProcessor processor(config, callback);
```

## Examples

### Real-time Demo
```bash
./build/examples/realtime_demo
```
Demonstrates real-time processing with synthetic PPG data.

### Offline Processing
```bash
./build/examples/offline_processing
```
Shows batch processing of PPG signals.

### Benchmark
```bash
./build/examples/benchmark
```
Performance benchmarking of different algorithms.

## Testing

```bash
cd build
make test
```

Or run tests directly:
```bash
./tests/test_preprocessing
./tests/test_hr_estimation
./tests/test_realtime
```

## Performance Targets

- **Latency**: < 50ms processing delay
- **Throughput**: > 1000 samples/second
- **Memory**: < 10MB total memory usage
- **CPU**: < 10% CPU usage on mobile processors
- **Accuracy**: Match Python implementation performance

## Integration

### Mobile Platforms
- **Android**: JNI wrapper for Java/Kotlin integration
- **iOS**: Objective-C++ wrapper for Swift integration
- **Cross-platform**: C API for other language bindings

### Embedded Systems
- **ARM Cortex-M**: Optimized for microcontrollers
- **RTOS**: Real-time operating system support
- **Low power**: Power-optimized algorithms

## Development Roadmap

1. **Phase 1**: Complete core algorithm implementations
2. **Phase 2**: Optimize for real-time performance
3. **Phase 3**: Mobile platform integration
4. **Phase 4**: Embedded system optimization
5. **Phase 5**: Production deployment

## Contributing

This implementation will be developed after the Python algorithms are finalized. The current structure provides the foundation for the full implementation.

## License

Same license as the main project (see LICENSE file in repository root).

## Contact

For questions about the C++ implementation, please refer to the main project documentation.
