# BSPML C++ Real-time Implementation for ESP32

This directory contains the C++ implementation of the BSPML (Biosignal Processing and Machine Learning) library for real-time PPG signal processing and heart rate estimation, specifically optimized for ESP32 microcontrollers.

## Overview

The C++ implementation is designed for deployment on ESP32 microcontrollers where computational resources are limited. Due to these constraints, the implementation uses a hybrid approach where basic processing is done on-device while complex algorithms may be streamed to a more powerful external processor.

## Current Status

**⚠️ PLACEHOLDER IMPLEMENTATION ⚠️**

This is currently a placeholder implementation with the following structure in place:
- Complete header files with full API definitions
- CMake build system configuration
- Basic implementation stubs
- Example applications
- Test framework setup

The full implementation will be completed after the Python algorithms are finalized and validated.

## Architecture

```
cpp_realtime/
├── include/bspml/          # Public header files
│   ├── preprocessing/      # Signal preprocessing headers
│   ├── hr_estimation/      # Heart rate estimation headers
│   ├── realtime/          # Real-time processing headers
│   ├── utils/             # Utility headers
│   └── bspml.h            # Main library header
├── src/                   # Implementation files
│   ├── preprocessing/     # Preprocessing implementations
│   ├── hr_estimation/     # HR estimation implementations
│   ├── realtime/          # Real-time processing implementations
│   └── utils/             # Utility implementations
├── examples/              # Example applications
├── tests/                 # Unit tests
└── CMakeLists.txt         # Build configuration
```

## Features (Planned)

### ESP32-Optimized Processing
- **On-device algorithms**: Basic filtering, peak detection, simple HR calculation
- **Streaming capability**: Send raw/preprocessed data to external processor
- **Hybrid processing**: Complex algorithms (wavelet, RLS) handled externally
- **Power optimization**: Efficient algorithms for battery-powered operation

### Signal Processing Strategy
- **Local processing**: Band-pass filtering (0.5-4 Hz), basic detrending
- **External processing**: Wavelet detrending, RLS motion artifact removal
- **Fallback algorithms**: Simple alternatives when external processing unavailable
- **Real-time constraints**: Sub-50ms processing latency

### ESP32 Integration
- **Hardware utilization**: ADC, timers, WiFi/Bluetooth for communication
- **FreeRTOS integration**: Real-time task management
- **Memory management**: Fixed allocation, no dynamic memory
- **Communication protocols**: WiFi/Bluetooth streaming to external devices

## Building

### Prerequisites
- CMake 3.12 or higher
- C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2017+)
- Optional: Eigen3 for linear algebra operations
- Optional: FFTW3 for frequency domain processing

### Build Instructions

```bash
cd cpp_realtime
mkdir build && cd build
cmake ..
make -j$(nproc)
```

### Build Options
```bash
# Release build (optimized)
cmake -DCMAKE_BUILD_TYPE=Release ..

# Debug build
cmake -DCMAKE_BUILD_TYPE=Debug ..

# Disable tests
cmake -DBUILD_TESTS=OFF ..
```

## Usage

### Basic Usage
```cpp
#include "bspml/bspml.h"

// Initialize library
bspml::initialize();

// Create processor
bspml::realtime::RealtimeConfig config;
config.sampling_rate = 64.0;
bspml::realtime::RealtimeProcessor processor(config);

// Process samples
double hr = processor.processSample(ppg_sample, acc_x, acc_y, acc_z);

// Cleanup
bspml::cleanup();
```

### Real-time Processing with Callbacks
```cpp
class MyCallback : public bspml::realtime::HeartRateCallback {
public:
    void onHeartRateUpdate(const bspml::realtime::RealtimeResult& result) override {
        if (result.valid) {
            std::cout << "HR: " << result.heart_rate_bpm << " BPM" << std::endl;
        }
    }
    
    void onSignalQualityChange(double quality) override {
        std::cout << "Quality: " << quality << std::endl;
    }
};

auto callback = std::make_shared<MyCallback>();
bspml::realtime::CallbackProcessor processor(config, callback);
```

## Examples

### Real-time Demo
```bash
./build/examples/realtime_demo
```
Demonstrates real-time processing with synthetic PPG data.

### Offline Processing
```bash
./build/examples/offline_processing
```
Shows batch processing of PPG signals.

### Benchmark
```bash
./build/examples/benchmark
```
Performance benchmarking of different algorithms.

## Testing

```bash
cd build
make test
```

Or run tests directly:
```bash
./tests/test_preprocessing
./tests/test_hr_estimation
./tests/test_realtime
```

## Performance Targets for ESP32

- **Latency**: < 50ms processing delay for on-device algorithms
- **Memory**: < 100KB RAM usage for signal processing
- **Power**: < 50mA average current consumption
- **Accuracy**: Maintain reasonable HR accuracy with simplified algorithms
- **Communication**: < 1s latency for external processing round-trip

## ESP32 Integration Strategy

### On-Device Processing
- **Basic filtering**: Simple IIR filters for noise reduction
- **Peak detection**: Lightweight local maxima detection
- **HR calculation**: Basic inter-beat interval to HR conversion
- **Quality assessment**: Simple signal quality metrics

### External Processing Communication
- **WiFi streaming**: Send preprocessed data to external device
- **Bluetooth**: Alternative communication for mobile integration
- **Data compression**: Efficient data transmission protocols
- **Fallback modes**: Continue basic processing if connection lost

## Development Roadmap

1. **Phase 1**: Complete core algorithm implementations
2. **Phase 2**: Optimize for real-time performance
3. **Phase 3**: Mobile platform integration
4. **Phase 4**: Embedded system optimization
5. **Phase 5**: Production deployment

## Contributing

This implementation will be developed after the Python algorithms are finalized. The current structure provides the foundation for the full implementation.

## License

Same license as the main project (see LICENSE file in repository root).

## Contact

For questions about the C++ implementation, please refer to the main project documentation.
