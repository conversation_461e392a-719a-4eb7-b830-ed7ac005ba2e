cmake_minimum_required(VERSION 3.12)

# Example executables
add_executable(realtime_demo realtime_demo.cpp)
target_link_libraries(realtime_demo bspml_static)

add_executable(offline_processing offline_processing.cpp)
target_link_libraries(offline_processing bspml_static)

add_executable(benchmark benchmark.cpp)
target_link_libraries(benchmark bspml_static)

# Install examples
install(TARGETS realtime_demo offline_processing benchmark
    RUNTIME DESTINATION bin/examples
)
