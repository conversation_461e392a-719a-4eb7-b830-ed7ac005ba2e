/**
 * @file realtime_demo.cpp
 * @brief Demonstration of real-time PPG processing
 * 
 * This example shows how to use the BSPML library for real-time
 * heart rate estimation from PPG signals.
 */

#include "bspml/bspml.h"
#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <random>

using namespace bspml;

/**
 * @brief Simple callback for heart rate updates
 */
class SimpleCallback : public realtime::HeartRateCallback {
public:
    void onHeartRateUpdate(const realtime::RealtimeResult& result) override {
        if (result.valid) {
            std::cout << "Heart Rate: " << result.heart_rate_bpm << " BPM, "
                     << "Quality: " << result.signal_quality << ", "
                     << "Confidence: " << result.confidence << std::endl;
        }
    }
    
    void onSignalQualityChange(double quality) override {
        std::cout << "Signal quality changed: " << quality << std::endl;
    }
};

/**
 * @brief Generate synthetic PPG signal for demonstration
 */
std::vector<double> generateSyntheticPPG(double duration, double sampling_rate, double heart_rate_bpm) {
    size_t num_samples = static_cast<size_t>(duration * sampling_rate);
    std::vector<double> ppg_signal(num_samples);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::normal_distribution<> noise(0.0, 0.05);
    
    double hr_freq = heart_rate_bpm / 60.0;  // Convert to Hz
    
    for (size_t i = 0; i < num_samples; ++i) {
        double t = i / sampling_rate;
        
        // Basic PPG waveform (simplified)
        double ppg = std::sin(2 * M_PI * hr_freq * t) + 
                    0.3 * std::sin(4 * M_PI * hr_freq * t);
        
        // Add baseline drift
        ppg += 0.1 * std::sin(2 * M_PI * 0.1 * t);
        
        // Add noise
        ppg += noise(gen);
        
        ppg_signal[i] = ppg;
    }
    
    return ppg_signal;
}

/**
 * @brief Demonstrate real-time processing
 */
void demonstrateRealtimeProcessing() {
    std::cout << "=== Real-time PPG Processing Demo ===" << std::endl;
    
    // Configuration
    realtime::RealtimeConfig config;
    config.sampling_rate = 64.0;
    config.buffer_length_seconds = 15.0;
    config.update_interval_seconds = 2.0;
    config.enable_preprocessing = true;
    config.enable_motion_removal = false;  // No accelerometer in this demo
    
    // Create processor with callback
    auto callback = std::make_shared<SimpleCallback>();
    realtime::CallbackProcessor processor(config, callback);
    
    // Generate synthetic data
    double duration = 30.0;  // 30 seconds
    double heart_rate = 75.0;  // 75 BPM
    auto ppg_data = generateSyntheticPPG(duration, config.sampling_rate, heart_rate);
    
    std::cout << "Processing " << ppg_data.size() << " samples..." << std::endl;
    std::cout << "Expected heart rate: " << heart_rate << " BPM" << std::endl;
    std::cout << std::endl;
    
    // Simulate real-time processing
    double sample_interval = 1.0 / config.sampling_rate;
    
    for (size_t i = 0; i < ppg_data.size(); ++i) {
        // Process sample
        auto result = processor.processPPGSample(ppg_data[i]);
        
        // Simulate real-time delay
        std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(sample_interval * 1000)));
        
        // Print progress every 5 seconds
        if (i % static_cast<size_t>(5 * config.sampling_rate) == 0) {
            double progress = static_cast<double>(i) / ppg_data.size() * 100.0;
            std::cout << "Progress: " << progress << "%" << std::endl;
        }
    }
    
    // Final statistics
    auto stats = processor.getProcessingStats();
    std::cout << std::endl << "Processing Statistics:" << std::endl;
    std::cout << "Total samples processed: " << stats.total_samples_processed << std::endl;
    std::cout << "Valid HR estimates: " << stats.valid_hr_estimates << std::endl;
    std::cout << "Average processing time: " << stats.average_processing_time_ms << " ms" << std::endl;
    std::cout << "Final HR estimate: " << processor.getCurrentHeartRate() << " BPM" << std::endl;
    std::cout << "Final signal quality: " << processor.getCurrentSignalQuality() << std::endl;
}

/**
 * @brief Demonstrate batch processing
 */
void demonstrateBatchProcessing() {
    std::cout << std::endl << "=== Batch Processing Demo ===" << std::endl;
    
    // Generate test data
    double duration = 60.0;  // 1 minute
    double sampling_rate = 64.0;
    double heart_rate = 80.0;  // 80 BPM
    
    auto ppg_data = generateSyntheticPPG(duration, sampling_rate, heart_rate);
    
    std::cout << "Processing " << ppg_data.size() << " samples in batch mode..." << std::endl;
    std::cout << "Expected heart rate: " << heart_rate << " BPM" << std::endl;
    
    // Use convenience function
    auto result = estimateHeartRate(ppg_data, sampling_rate);
    
    if (result.success) {
        std::cout << "Estimated heart rate: " << result.heart_rate_bpm << " BPM" << std::endl;
        std::cout << "Confidence: " << result.confidence << std::endl;
        std::cout << "Number of time points: " << result.time_points.size() << std::endl;
        
        if (!result.hr_values.empty()) {
            double mean_hr = 0.0;
            for (double hr : result.hr_values) {
                mean_hr += hr;
            }
            mean_hr /= result.hr_values.size();
            
            std::cout << "Mean heart rate: " << mean_hr << " BPM" << std::endl;
        }
    } else {
        std::cout << "Heart rate estimation failed: " << result.error_message << std::endl;
    }
}

/**
 * @brief Main function
 */
int main() {
    std::cout << "BSPML Real-time Processing Demo" << std::endl;
    std::cout << "Library version: " << getVersion() << std::endl;
    std::cout << std::endl;
    
    // Initialize library
    if (!initialize()) {
        std::cerr << "Failed to initialize BSPML library" << std::endl;
        return 1;
    }
    
    try {
        // Run demonstrations
        demonstrateRealtimeProcessing();
        demonstrateBatchProcessing();
        
        std::cout << std::endl << "Demo completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error during demo: " << e.what() << std::endl;
        cleanup();
        return 1;
    }
    
    // Cleanup
    cleanup();
    return 0;
}
