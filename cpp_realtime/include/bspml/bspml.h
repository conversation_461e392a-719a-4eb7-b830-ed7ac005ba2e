/**
 * @file bspml.h
 * @brief Main header file for BSPML (Biosignal Processing and Machine Learning) library
 * 
 * This header provides the main interface for real-time PPG signal processing
 * and heart rate estimation algorithms.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

#ifndef BSPML_H
#define BSPML_H

#include <vector>
#include <memory>
#include <string>

// Core preprocessing modules
#include "bspml/preprocessing/detrending.h"
#include "bspml/preprocessing/denoising.h"
#include "bspml/preprocessing/motion_artifacts.h"
#include "bspml/preprocessing/pipeline.h"

// Heart rate estimation modules
#include "bspml/hr_estimation/peak_detection.h"
#include "bspml/hr_estimation/hr_calculation.h"
#include "bspml/hr_estimation/pipeline.h"

// Real-time processing modules
#include "bspml/realtime/realtime_processor.h"
#include "bspml/realtime/circular_buffer.h"

// Utility modules
#include "bspml/utils/signal_utils.h"
#include "bspml/utils/math_utils.h"

namespace bspml {

/**
 * @brief Library version information
 */
struct Version {
    static constexpr int MAJOR = 1;
    static constexpr int MINOR = 0;
    static constexpr int PATCH = 0;
    
    static std::string getString() {
        return std::to_string(MAJOR) + "." + 
               std::to_string(MINOR) + "." + 
               std::to_string(PATCH);
    }
};

/**
 * @brief Signal processing configuration
 */
struct ProcessingConfig {
    // Sampling rate
    double sampling_rate = 64.0;
    
    // Preprocessing options
    bool enable_detrending = true;
    bool enable_denoising = true;
    bool enable_motion_removal = true;
    
    // Detrending parameters
    std::string detrending_method = "wavelet";
    int wavelet_levels = 6;
    
    // Denoising parameters
    double low_cutoff = 0.5;
    double high_cutoff = 4.0;
    int filter_order = 4;
    
    // Motion artifact removal parameters
    int rls_filter_order = 8;
    double rls_forgetting_factor = 0.99;
    
    // Heart rate estimation parameters
    double window_size = 10.0;
    double overlap = 0.5;
    double min_peak_distance = 0.3;
};

/**
 * @brief Heart rate estimation results
 */
struct HRResult {
    bool success = false;
    double heart_rate_bpm = 0.0;
    double confidence = 0.0;
    std::vector<double> time_points;
    std::vector<double> hr_values;
    std::string error_message;
};

/**
 * @brief Signal quality metrics
 */
struct QualityMetrics {
    double snr_db = 0.0;
    double signal_quality_index = 0.0;
    double motion_correlation = 0.0;
    bool is_reliable = false;
};

/**
 * @brief Main BSPML processor class
 * 
 * This class provides a high-level interface for PPG signal processing
 * and heart rate estimation, suitable for both offline and real-time applications.
 */
class BSPMLProcessor {
public:
    /**
     * @brief Constructor
     * @param config Processing configuration
     */
    explicit BSPMLProcessor(const ProcessingConfig& config = ProcessingConfig{});
    
    /**
     * @brief Destructor
     */
    ~BSPMLProcessor();
    
    /**
     * @brief Process PPG signal and estimate heart rate
     * @param ppg_signal PPG signal samples
     * @param acc_signals Accelerometer signals (optional, can be empty)
     * @return Heart rate estimation results
     */
    HRResult processSignal(const std::vector<double>& ppg_signal,
                          const std::vector<std::vector<double>>& acc_signals = {});
    
    /**
     * @brief Process single sample for real-time applications
     * @param ppg_sample PPG sample
     * @param acc_samples Accelerometer samples (x, y, z)
     * @return Current heart rate estimate (0 if not available)
     */
    double processSample(double ppg_sample, 
                        const std::vector<double>& acc_samples = {});
    
    /**
     * @brief Get signal quality metrics
     * @return Quality metrics for the last processed signal
     */
    QualityMetrics getQualityMetrics() const;
    
    /**
     * @brief Reset processor state
     */
    void reset();
    
    /**
     * @brief Update processing configuration
     * @param config New configuration
     */
    void updateConfig(const ProcessingConfig& config);
    
    /**
     * @brief Get current configuration
     * @return Current processing configuration
     */
    const ProcessingConfig& getConfig() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Convenience function for offline PPG processing
 * @param ppg_signal PPG signal samples
 * @param sampling_rate Sampling rate in Hz
 * @param acc_signals Accelerometer signals (optional)
 * @return Heart rate estimation results
 */
HRResult estimateHeartRate(const std::vector<double>& ppg_signal,
                          double sampling_rate,
                          const std::vector<std::vector<double>>& acc_signals = {});

/**
 * @brief Convenience function for PPG preprocessing only
 * @param ppg_signal PPG signal samples
 * @param sampling_rate Sampling rate in Hz
 * @param acc_signals Accelerometer signals (optional)
 * @param config Processing configuration
 * @return Preprocessed PPG signal
 */
std::vector<double> preprocessPPG(const std::vector<double>& ppg_signal,
                                 double sampling_rate,
                                 const std::vector<std::vector<double>>& acc_signals = {},
                                 const ProcessingConfig& config = ProcessingConfig{});

/**
 * @brief Get library version
 * @return Version string
 */
std::string getVersion();

/**
 * @brief Initialize library (call once at startup)
 * @return True if initialization successful
 */
bool initialize();

/**
 * @brief Cleanup library resources (call once at shutdown)
 */
void cleanup();

} // namespace bspml

#endif // BSPML_H
