/**
 * @file pipeline.h
 * @brief PPG preprocessing pipeline for C++ implementation
 * 
 * This module provides the complete preprocessing pipeline including
 * detrending, denoising, and motion artifact removal.
 */

#ifndef BSPML_PREPROCESSING_PIPELINE_H
#define BSPML_PREPROCESSING_PIPELINE_H

#include <vector>
#include <memory>
#include <string>

namespace bspml {
namespace preprocessing {

/**
 * @brief Preprocessing configuration parameters
 */
struct PreprocessingConfig {
    // General parameters
    double sampling_rate = 64.0;
    
    // Processing steps
    bool enable_detrending = true;
    bool enable_denoising = true;
    bool enable_motion_removal = true;
    
    // Detrending parameters
    std::string detrending_method = "wavelet";
    int wavelet_levels = 6;
    double cutoff_frequency = 0.1;
    
    // Denoising parameters
    std::string denoising_method = "bandpass";
    double low_cutoff = 0.5;
    double high_cutoff = 4.0;
    int filter_order = 4;
    
    // Motion artifact removal parameters
    std::string motion_removal_method = "rls";
    int rls_filter_order = 8;
    double rls_forgetting_factor = 0.99;
    double nlms_step_size = 0.01;
};

/**
 * @brief Preprocessing result with quality metrics
 */
struct PreprocessingResult {
    std::vector<double> processed_signal;
    bool success = false;
    std::string error_message;
    
    // Quality metrics
    double snr_improvement_db = 0.0;
    double signal_preservation_ratio = 0.0;
    double noise_reduction_percent = 0.0;
    double motion_correlation_reduction = 0.0;
};

/**
 * @brief Main preprocessing pipeline class
 */
class PreprocessingPipeline {
public:
    /**
     * @brief Constructor
     * @param config Preprocessing configuration
     */
    explicit PreprocessingPipeline(const PreprocessingConfig& config = PreprocessingConfig{});
    
    /**
     * @brief Destructor
     */
    ~PreprocessingPipeline();
    
    /**
     * @brief Process PPG signal with optional accelerometer data
     * @param ppg_signal Input PPG signal
     * @param acc_signals Accelerometer signals (optional, 3 channels)
     * @return Preprocessing result
     */
    PreprocessingResult process(const std::vector<double>& ppg_signal,
                               const std::vector<std::vector<double>>& acc_signals = {});
    
    /**
     * @brief Process single sample for real-time applications
     * @param ppg_sample PPG sample
     * @param acc_samples Accelerometer samples (x, y, z)
     * @return Processed PPG sample
     */
    double processSample(double ppg_sample,
                        const std::vector<double>& acc_samples = {});
    
    /**
     * @brief Update configuration
     * @param config New configuration
     */
    void updateConfig(const PreprocessingConfig& config);
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    const PreprocessingConfig& getConfig() const;
    
    /**
     * @brief Reset pipeline state
     */
    void reset();
    
    /**
     * @brief Check if pipeline is ready for processing
     * @return True if ready
     */
    bool isReady() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Convenience function for offline preprocessing
 * @param ppg_signal PPG signal to process
 * @param sampling_rate Sampling rate in Hz
 * @param acc_signals Accelerometer signals (optional)
 * @param config Processing configuration
 * @return Preprocessed signal
 */
std::vector<double> preprocessPPG(const std::vector<double>& ppg_signal,
                                 double sampling_rate,
                                 const std::vector<std::vector<double>>& acc_signals = {},
                                 const PreprocessingConfig& config = PreprocessingConfig{});

/**
 * @brief Assess preprocessing quality
 * @param original_signal Original PPG signal
 * @param processed_signal Processed PPG signal
 * @param sampling_rate Sampling rate in Hz
 * @param acc_signals Accelerometer signals (optional)
 * @return Quality metrics
 */
struct QualityMetrics {
    double snr_improvement_db;
    double signal_preservation_ratio;
    double noise_reduction_percent;
    double motion_correlation_reduction;
    bool is_reliable;
};

QualityMetrics assessPreprocessingQuality(const std::vector<double>& original_signal,
                                         const std::vector<double>& processed_signal,
                                         double sampling_rate,
                                         const std::vector<std::vector<double>>& acc_signals = {});

} // namespace preprocessing
} // namespace bspml

#endif // BSPML_PREPROCESSING_PIPELINE_H
