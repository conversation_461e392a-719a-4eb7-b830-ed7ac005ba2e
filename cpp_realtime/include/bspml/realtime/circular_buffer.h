/**
 * @file circular_buffer.h
 * @brief Thread-safe circular buffer for real-time signal processing
 * 
 * This module provides efficient circular buffer implementations for
 * storing and processing continuous signal data streams.
 */

#ifndef BSPML_CIRCULAR_BUFFER_H
#define BSPML_CIRCULAR_BUFFER_H

#include <vector>
#include <mutex>
#include <atomic>
#include <memory>

namespace bspml {
namespace realtime {

/**
 * @brief Thread-safe circular buffer for signal data
 * 
 * This class implements a lock-free circular buffer optimized for
 * real-time signal processing applications.
 */
template<typename T>
class CircularBuffer {
public:
    /**
     * @brief Constructor
     * @param capacity Buffer capacity
     */
    explicit CircularBuffer(size_t capacity);
    
    /**
     * @brief Destructor
     */
    ~CircularBuffer() = default;
    
    /**
     * @brief Add single sample to buffer
     * @param sample Sample to add
     * @return True if successful, false if buffer is full
     */
    bool push(const T& sample);
    
    /**
     * @brief Add multiple samples to buffer
     * @param samples Vector of samples to add
     * @return Number of samples actually added
     */
    size_t push(const std::vector<T>& samples);
    
    /**
     * @brief Get most recent samples
     * @param count Number of samples to retrieve
     * @return Vector of most recent samples (may be fewer than requested)
     */
    std::vector<T> getRecent(size_t count) const;
    
    /**
     * @brief Get all samples in buffer
     * @return Vector of all samples in chronological order
     */
    std::vector<T> getAll() const;
    
    /**
     * @brief Get samples in specified range
     * @param start_offset Offset from most recent sample (0 = most recent)
     * @param count Number of samples to retrieve
     * @return Vector of samples
     */
    std::vector<T> getRange(size_t start_offset, size_t count) const;
    
    /**
     * @brief Get sample at specific offset from most recent
     * @param offset Offset from most recent (0 = most recent)
     * @return Sample value, or default T{} if offset is invalid
     */
    T at(size_t offset) const;
    
    /**
     * @brief Check if buffer is empty
     * @return True if empty
     */
    bool empty() const;
    
    /**
     * @brief Check if buffer is full
     * @return True if full
     */
    bool full() const;
    
    /**
     * @brief Get current number of samples in buffer
     * @return Number of samples
     */
    size_t size() const;
    
    /**
     * @brief Get buffer capacity
     * @return Maximum number of samples
     */
    size_t capacity() const;
    
    /**
     * @brief Clear all samples from buffer
     */
    void clear();
    
    /**
     * @brief Resize buffer (clears existing data)
     * @param new_capacity New buffer capacity
     */
    void resize(size_t new_capacity);

private:
    std::vector<T> buffer_;
    std::atomic<size_t> head_{0};
    std::atomic<size_t> tail_{0};
    std::atomic<size_t> size_{0};
    size_t capacity_;
    mutable std::mutex mutex_;
};

/**
 * @brief Multi-channel circular buffer for synchronized signals
 * 
 * This class manages multiple synchronized circular buffers,
 * useful for PPG + accelerometer data.
 */
template<typename T>
class MultiChannelBuffer {
public:
    /**
     * @brief Constructor
     * @param num_channels Number of channels
     * @param capacity Buffer capacity per channel
     */
    MultiChannelBuffer(size_t num_channels, size_t capacity);
    
    /**
     * @brief Add synchronized samples to all channels
     * @param samples Vector of samples (one per channel)
     * @return True if successful
     */
    bool push(const std::vector<T>& samples);
    
    /**
     * @brief Add sample to specific channel
     * @param channel Channel index
     * @param sample Sample value
     * @return True if successful
     */
    bool push(size_t channel, const T& sample);
    
    /**
     * @brief Get recent samples from all channels
     * @param count Number of samples per channel
     * @return Vector of vectors (channels x samples)
     */
    std::vector<std::vector<T>> getRecent(size_t count) const;
    
    /**
     * @brief Get recent samples from specific channel
     * @param channel Channel index
     * @param count Number of samples
     * @return Vector of samples
     */
    std::vector<T> getRecent(size_t channel, size_t count) const;
    
    /**
     * @brief Get all samples from all channels
     * @return Vector of vectors (channels x samples)
     */
    std::vector<std::vector<T>> getAll() const;
    
    /**
     * @brief Get number of channels
     * @return Number of channels
     */
    size_t getNumChannels() const;
    
    /**
     * @brief Get buffer capacity
     * @return Buffer capacity per channel
     */
    size_t capacity() const;
    
    /**
     * @brief Get current size (minimum across all channels)
     * @return Number of synchronized samples available
     */
    size_t size() const;
    
    /**
     * @brief Clear all channels
     */
    void clear();
    
    /**
     * @brief Check if buffers are synchronized
     * @return True if all channels have same number of samples
     */
    bool isSynchronized() const;

private:
    std::vector<std::unique_ptr<CircularBuffer<T>>> channels_;
    size_t num_channels_;
    mutable std::mutex sync_mutex_;
};

/**
 * @brief Specialized buffer for PPG and accelerometer data
 */
class PPGAccBuffer {
public:
    /**
     * @brief Constructor
     * @param capacity Buffer capacity
     */
    explicit PPGAccBuffer(size_t capacity);
    
    /**
     * @brief Add synchronized PPG and accelerometer sample
     * @param ppg_sample PPG sample
     * @param acc_x Accelerometer X sample
     * @param acc_y Accelerometer Y sample
     * @param acc_z Accelerometer Z sample
     * @return True if successful
     */
    bool push(double ppg_sample, double acc_x, double acc_y, double acc_z);
    
    /**
     * @brief Add PPG sample only
     * @param ppg_sample PPG sample
     * @return True if successful
     */
    bool pushPPG(double ppg_sample);
    
    /**
     * @brief Get recent PPG samples
     * @param count Number of samples
     * @return Vector of PPG samples
     */
    std::vector<double> getRecentPPG(size_t count) const;
    
    /**
     * @brief Get recent accelerometer samples
     * @param count Number of samples
     * @return Vector of vectors (3 x count) for x, y, z axes
     */
    std::vector<std::vector<double>> getRecentAcc(size_t count) const;
    
    /**
     * @brief Get synchronized PPG and accelerometer data
     * @param count Number of samples
     * @return Pair of (PPG samples, ACC samples)
     */
    std::pair<std::vector<double>, std::vector<std::vector<double>>> 
    getRecentSynchronized(size_t count) const;
    
    /**
     * @brief Check if accelerometer data is available
     * @return True if ACC data is being stored
     */
    bool hasAccelerometerData() const;
    
    /**
     * @brief Get buffer capacity
     * @return Buffer capacity
     */
    size_t capacity() const;
    
    /**
     * @brief Get current size
     * @return Number of samples
     */
    size_t size() const;
    
    /**
     * @brief Clear all data
     */
    void clear();

private:
    std::unique_ptr<MultiChannelBuffer<double>> buffer_;
    bool has_acc_data_;
};

// Template implementation
template<typename T>
CircularBuffer<T>::CircularBuffer(size_t capacity) 
    : buffer_(capacity), capacity_(capacity) {
}

template<typename T>
bool CircularBuffer<T>::push(const T& sample) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    buffer_[head_] = sample;
    head_ = (head_ + 1) % capacity_;
    
    if (size_ < capacity_) {
        size_++;
    } else {
        tail_ = (tail_ + 1) % capacity_;
    }
    
    return true;
}

template<typename T>
size_t CircularBuffer<T>::push(const std::vector<T>& samples) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t added = 0;
    for (const auto& sample : samples) {
        buffer_[head_] = sample;
        head_ = (head_ + 1) % capacity_;
        
        if (size_ < capacity_) {
            size_++;
        } else {
            tail_ = (tail_ + 1) % capacity_;
        }
        added++;
    }
    
    return added;
}

template<typename T>
std::vector<T> CircularBuffer<T>::getRecent(size_t count) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    count = std::min(count, size_.load());
    std::vector<T> result;
    result.reserve(count);
    
    size_t start_pos = (head_ + capacity_ - count) % capacity_;
    
    for (size_t i = 0; i < count; ++i) {
        result.push_back(buffer_[(start_pos + i) % capacity_]);
    }
    
    return result;
}

template<typename T>
std::vector<T> CircularBuffer<T>::getAll() const {
    return getRecent(size_);
}

template<typename T>
T CircularBuffer<T>::at(size_t offset) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (offset >= size_) {
        return T{};
    }
    
    size_t pos = (head_ + capacity_ - 1 - offset) % capacity_;
    return buffer_[pos];
}

template<typename T>
bool CircularBuffer<T>::empty() const {
    return size_ == 0;
}

template<typename T>
bool CircularBuffer<T>::full() const {
    return size_ == capacity_;
}

template<typename T>
size_t CircularBuffer<T>::size() const {
    return size_;
}

template<typename T>
size_t CircularBuffer<T>::capacity() const {
    return capacity_;
}

template<typename T>
void CircularBuffer<T>::clear() {
    std::lock_guard<std::mutex> lock(mutex_);
    head_ = 0;
    tail_ = 0;
    size_ = 0;
}

} // namespace realtime
} // namespace bspml

#endif // BSPML_CIRCULAR_BUFFER_H
