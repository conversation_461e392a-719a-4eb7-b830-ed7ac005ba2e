/**
 * @file realtime_processor.h
 * @brief Real-time PPG signal processor for heart rate estimation
 * 
 * This module provides real-time processing capabilities for continuous
 * PPG signal analysis and heart rate estimation.
 */

#ifndef BSPML_REALTIME_PROCESSOR_H
#define BSPML_REALTIME_PROCESSOR_H

#include <vector>
#include <memory>
#include <chrono>

namespace bspml {
namespace realtime {

/**
 * @brief Real-time processing configuration
 */
struct RealtimeConfig {
    double sampling_rate = 64.0;           ///< Sampling rate in Hz
    double buffer_length_seconds = 15.0;   ///< Buffer length in seconds
    double update_interval_seconds = 1.0;  ///< HR update interval in seconds
    double min_signal_quality = 0.5;       ///< Minimum signal quality threshold
    
    // Preprocessing parameters
    bool enable_preprocessing = true;
    int filter_order = 4;
    double low_cutoff = 0.5;
    double high_cutoff = 4.0;
    
    // Peak detection parameters
    double min_peak_distance = 0.3;        ///< Minimum peak distance in seconds
    double peak_threshold_factor = 0.6;    ///< Peak detection threshold factor
    
    // Motion artifact removal
    bool enable_motion_removal = true;
    int rls_order = 8;
    double rls_lambda = 0.99;
};

/**
 * @brief Real-time processing result
 */
struct RealtimeResult {
    bool valid = false;                     ///< Whether result is valid
    double heart_rate_bpm = 0.0;           ///< Current heart rate estimate
    double confidence = 0.0;               ///< Confidence in estimate (0-1)
    double signal_quality = 0.0;           ///< Signal quality index (0-1)
    std::chrono::steady_clock::time_point timestamp; ///< Result timestamp
    
    // Additional metrics
    double rr_interval_ms = 0.0;           ///< Last RR interval in milliseconds
    int num_peaks_detected = 0;            ///< Number of peaks in current window
    double snr_estimate = 0.0;             ///< Signal-to-noise ratio estimate
};

/**
 * @brief Real-time PPG processor
 * 
 * This class implements a real-time PPG signal processor that continuously
 * analyzes incoming PPG and accelerometer data to provide heart rate estimates.
 */
class RealtimeProcessor {
public:
    /**
     * @brief Constructor
     * @param config Real-time processing configuration
     */
    explicit RealtimeProcessor(const RealtimeConfig& config = RealtimeConfig{});
    
    /**
     * @brief Destructor
     */
    ~RealtimeProcessor();
    
    /**
     * @brief Process a single PPG sample
     * @param ppg_sample PPG sample value
     * @param timestamp Sample timestamp (optional, uses current time if not provided)
     * @return Processing result (may be invalid if not enough data)
     */
    RealtimeResult processPPGSample(double ppg_sample,
                                   std::chrono::steady_clock::time_point timestamp = 
                                   std::chrono::steady_clock::now());
    
    /**
     * @brief Process PPG sample with accelerometer data
     * @param ppg_sample PPG sample value
     * @param acc_x Accelerometer X-axis sample
     * @param acc_y Accelerometer Y-axis sample
     * @param acc_z Accelerometer Z-axis sample
     * @param timestamp Sample timestamp (optional)
     * @return Processing result
     */
    RealtimeResult processSample(double ppg_sample,
                                double acc_x, double acc_y, double acc_z,
                                std::chrono::steady_clock::time_point timestamp = 
                                std::chrono::steady_clock::now());
    
    /**
     * @brief Process batch of samples
     * @param ppg_samples PPG samples
     * @param acc_samples Accelerometer samples (3 columns: x, y, z)
     * @return Vector of processing results
     */
    std::vector<RealtimeResult> processBatch(const std::vector<double>& ppg_samples,
                                           const std::vector<std::vector<double>>& acc_samples = {});
    
    /**
     * @brief Get current heart rate estimate
     * @return Current heart rate in BPM (0 if not available)
     */
    double getCurrentHeartRate() const;
    
    /**
     * @brief Get current signal quality
     * @return Signal quality index (0-1)
     */
    double getCurrentSignalQuality() const;
    
    /**
     * @brief Check if processor has sufficient data for reliable estimates
     * @return True if sufficient data available
     */
    bool isReady() const;
    
    /**
     * @brief Reset processor state
     */
    void reset();
    
    /**
     * @brief Update configuration
     * @param config New configuration
     */
    void updateConfig(const RealtimeConfig& config);
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    const RealtimeConfig& getConfig() const;
    
    /**
     * @brief Get processing statistics
     * @return Statistics about processing performance
     */
    struct ProcessingStats {
        size_t total_samples_processed = 0;
        size_t valid_hr_estimates = 0;
        double average_processing_time_ms = 0.0;
        double buffer_fill_ratio = 0.0;
        std::chrono::steady_clock::time_point last_update;
    };
    
    ProcessingStats getProcessingStats() const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * @brief Callback interface for real-time heart rate updates
 */
class HeartRateCallback {
public:
    virtual ~HeartRateCallback() = default;
    
    /**
     * @brief Called when new heart rate estimate is available
     * @param result Processing result with heart rate estimate
     */
    virtual void onHeartRateUpdate(const RealtimeResult& result) = 0;
    
    /**
     * @brief Called when signal quality changes significantly
     * @param quality New signal quality (0-1)
     */
    virtual void onSignalQualityChange(double quality) = 0;
};

/**
 * @brief Real-time processor with callback support
 * 
 * This class extends RealtimeProcessor with callback functionality
 * for event-driven applications.
 */
class CallbackProcessor : public RealtimeProcessor {
public:
    /**
     * @brief Constructor
     * @param config Processing configuration
     * @param callback Callback interface for updates
     */
    CallbackProcessor(const RealtimeConfig& config, 
                     std::shared_ptr<HeartRateCallback> callback);
    
    /**
     * @brief Set callback interface
     * @param callback New callback interface
     */
    void setCallback(std::shared_ptr<HeartRateCallback> callback);
    
    /**
     * @brief Remove callback interface
     */
    void removeCallback();

private:
    std::shared_ptr<HeartRateCallback> callback_;
};

} // namespace realtime
} // namespace bspml

#endif // BSPML_REALTIME_PROCESSOR_H
