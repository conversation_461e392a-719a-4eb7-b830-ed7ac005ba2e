# Include important files
include README.md
include LICENSE
include requirements.txt
include requirements-dev.txt
include pyproject.toml
include pytest.ini

# Include documentation
recursive-include docs *
prune docs/_build

# Include examples and notebooks
recursive-include examples *.py *.ipynb *.md
recursive-include notebooks *.ipynb *.md

# Include test data
recursive-include bspml/data *.csv *.h5 *.json *.yaml

# Include configuration files
recursive-include bspml/config *.yaml *.json

# Exclude development and build files
exclude .gitignore
exclude .pre-commit-config.yaml
exclude tox.ini
exclude Makefile
prune .git
prune .github
prune .pytest_cache
prune .mypy_cache
prune __pycache__
prune *.egg-info
prune build
prune dist
prune htmlcov

# Exclude compiled files
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .DS_Store
global-exclude *.so
