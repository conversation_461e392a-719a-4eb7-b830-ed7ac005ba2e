"""
Biosignal Processing and Machine Learning (BSPML) Package

A comprehensive package for PPG signal preprocessing and heart rate estimation.

This package provides:
- Signal preprocessing (detrending, denoising, motion artifact removal)
- Heart rate estimation algorithms
- Data handling utilities
- Performance evaluation tools
"""

__version__ = "0.1.0"
__author__ = "BSPML Team"
__email__ = "<EMAIL>"

# Import main modules for easy access
from . import preprocessing
from . import hr_estimation

# Import key functions for convenience
from .preprocessing import preprocess_ppg
from .hr_estimation import estimate_heart_rate

__all__ = [
    "preprocessing",
    "hr_estimation",
    "preprocess_ppg",
    "estimate_heart_rate"
]
