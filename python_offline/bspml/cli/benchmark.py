"""
Command-line interface for benchmarking BSPML algorithms.

This module provides performance benchmarking tools for different
signal processing and heart rate estimation methods.
"""

import click
import time
import numpy as np
import pandas as pd
from pathlib import Path
import json
import logging
import psutil
import os
from typing import Dict, Any, List

from bspml.preprocessing import preprocess_ppg
from bspml.hr_estimation import estimate_heart_rate


@click.command()
@click.option('--duration', '-d', default=60.0, help='Signal duration in seconds')
@click.option('--sampling-rate', '-sr', default=64.0, help='Sampling rate in Hz')
@click.option('--heart-rate', '-hr', default=75.0, help='Simulated heart rate in BPM')
@click.option('--noise-level', default=0.05, help='Noise level (0-1)')
@click.option('--iterations', '-n', default=10, help='Number of benchmark iterations')
@click.option('--methods', default='all', help='Methods to benchmark (comma-separated or "all")')
@click.option('--output', '-o', type=click.Path(), help='Output file for results')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def main(duration, sampling_rate, heart_rate, noise_level, iterations, methods, output, verbose):
    """
    Benchmark BSPML algorithm performance.
    
    This tool generates synthetic PPG signals and measures the performance
    of different preprocessing and heart rate estimation methods.
    """
    # Setup logging
    logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)
    logger = logging.getLogger(__name__)
    
    try:
        # Parse methods
        if methods == 'all':
            method_list = ['sliding_window', 'adaptive', 'template']
        else:
            method_list = [m.strip() for m in methods.split(',')]
        
        logger.info(f"Benchmarking methods: {method_list}")
        logger.info(f"Signal parameters: {duration}s, {sampling_rate}Hz, {heart_rate}BPM")
        logger.info(f"Running {iterations} iterations per method")
        
        # Generate synthetic test data
        num_samples = int(duration * sampling_rate)
        t = np.linspace(0, duration, num_samples)
        
        # Create base PPG signal
        hr_freq = heart_rate / 60.0
        ppg_clean = np.sin(2 * np.pi * hr_freq * t) + 0.3 * np.sin(4 * np.pi * hr_freq * t)
        
        # Add artifacts
        baseline_drift = 0.1 * np.sin(2 * np.pi * 0.1 * t)
        noise = noise_level * np.random.randn(num_samples)
        ppg_signal = ppg_clean + baseline_drift + noise
        
        # Generate accelerometer data
        acc_x = 0.1 * np.random.randn(num_samples)
        acc_y = 0.1 * np.random.randn(num_samples)
        acc_z = 0.1 * np.random.randn(num_samples)
        acc_signals = np.column_stack([acc_x, acc_y, acc_z])
        
        logger.info(f"Generated synthetic data: {len(ppg_signal)} samples")
        
        # Benchmark results storage
        benchmark_results = {
            'test_parameters': {
                'duration': duration,
                'sampling_rate': sampling_rate,
                'heart_rate': heart_rate,
                'noise_level': noise_level,
                'iterations': iterations,
                'num_samples': num_samples
            },
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'python_version': f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}"
            },
            'methods': {},
            'summary': {}
        }
        
        # Benchmark each method
        for method in method_list:
            click.echo(f"\nBenchmarking {method} method...")
            
            method_results = {
                'method': method,
                'iterations': [],
                'statistics': {}
            }
            
            processing_times = []
            memory_usage = []
            success_count = 0
            hr_estimates = []
            
            for i in range(iterations):
                if verbose:
                    click.echo(f"  Iteration {i+1}/{iterations}")
                
                # Measure memory before processing
                process = psutil.Process()
                memory_before = process.memory_info().rss / (1024**2)  # MB
                
                # Time the processing
                start_time = time.perf_counter()
                
                try:
                    # Preprocess signal
                    processed_ppg = preprocess_ppg(
                        ppg_signal.copy(),
                        acc_signals=acc_signals.copy(),
                        sampling_rate=sampling_rate
                    )
                    
                    # Estimate heart rate
                    hr_result = estimate_heart_rate(
                        processed_ppg,
                        sampling_rate,
                        peak_detection_method=method,
                        return_statistics=False
                    )
                    
                    end_time = time.perf_counter()
                    processing_time = (end_time - start_time) * 1000  # ms
                    
                    # Measure memory after processing
                    memory_after = process.memory_info().rss / (1024**2)  # MB
                    memory_used = memory_after - memory_before
                    
                    if hr_result['success'] and hr_result['hr_values']:
                        success_count += 1
                        mean_hr = np.mean(hr_result['hr_values'])
                        hr_estimates.append(mean_hr)
                    else:
                        mean_hr = 0
                    
                    # Store iteration results
                    iteration_result = {
                        'iteration': i + 1,
                        'processing_time_ms': processing_time,
                        'memory_usage_mb': memory_used,
                        'success': hr_result['success'],
                        'estimated_hr_bpm': mean_hr,
                        'num_hr_estimates': len(hr_result.get('hr_values', []))
                    }
                    
                    method_results['iterations'].append(iteration_result)
                    processing_times.append(processing_time)
                    memory_usage.append(memory_used)
                    
                except Exception as e:
                    logger.warning(f"Iteration {i+1} failed: {str(e)}")
                    iteration_result = {
                        'iteration': i + 1,
                        'processing_time_ms': 0,
                        'memory_usage_mb': 0,
                        'success': False,
                        'estimated_hr_bpm': 0,
                        'error': str(e)
                    }
                    method_results['iterations'].append(iteration_result)
            
            # Calculate statistics
            if processing_times:
                method_results['statistics'] = {
                    'success_rate': success_count / iterations,
                    'mean_processing_time_ms': np.mean(processing_times),
                    'std_processing_time_ms': np.std(processing_times),
                    'min_processing_time_ms': np.min(processing_times),
                    'max_processing_time_ms': np.max(processing_times),
                    'mean_memory_usage_mb': np.mean(memory_usage),
                    'std_memory_usage_mb': np.std(memory_usage),
                    'throughput_samples_per_second': num_samples / (np.mean(processing_times) / 1000),
                }
                
                if hr_estimates:
                    method_results['statistics'].update({
                        'mean_estimated_hr_bpm': np.mean(hr_estimates),
                        'std_estimated_hr_bpm': np.std(hr_estimates),
                        'hr_accuracy_error_bpm': abs(np.mean(hr_estimates) - heart_rate),
                        'hr_precision_std_bpm': np.std(hr_estimates)
                    })
            
            benchmark_results['methods'][method] = method_results
            
            # Print method summary
            stats = method_results['statistics']
            click.echo(f"  Success rate: {stats.get('success_rate', 0):.1%}")
            click.echo(f"  Mean processing time: {stats.get('mean_processing_time_ms', 0):.1f} ms")
            click.echo(f"  Throughput: {stats.get('throughput_samples_per_second', 0):.0f} samples/s")
            if 'mean_estimated_hr_bpm' in stats:
                click.echo(f"  HR accuracy: {stats.get('hr_accuracy_error_bpm', 0):.1f} BPM error")
        
        # Calculate overall summary
        all_methods_stats = [results['statistics'] for results in benchmark_results['methods'].values() 
                           if results['statistics']]
        
        if all_methods_stats:
            benchmark_results['summary'] = {
                'fastest_method': min(benchmark_results['methods'].items(), 
                                    key=lambda x: x[1]['statistics'].get('mean_processing_time_ms', float('inf')))[0],
                'most_accurate_method': min(benchmark_results['methods'].items(),
                                          key=lambda x: x[1]['statistics'].get('hr_accuracy_error_bpm', float('inf')))[0],
                'most_reliable_method': max(benchmark_results['methods'].items(),
                                          key=lambda x: x[1]['statistics'].get('success_rate', 0))[0]
            }
        
        # Save results
        if output:
            output_path = Path(output)
        else:
            output_path = Path(f'benchmark_results_{int(time.time())}.json')
        
        logger.info(f"Saving benchmark results to {output_path}")
        with open(output_path, 'w') as f:
            json.dump(benchmark_results, f, indent=2)
        
        # Print final summary
        click.echo(f"\n✓ Benchmark completed successfully!")
        click.echo(f"  Results saved to: {output_path}")
        click.echo(f"  Methods tested: {len(method_list)}")
        click.echo(f"  Total iterations: {iterations * len(method_list)}")
        
        if benchmark_results['summary']:
            summary = benchmark_results['summary']
            click.echo(f"\nSummary:")
            click.echo(f"  Fastest method: {summary.get('fastest_method', 'N/A')}")
            click.echo(f"  Most accurate method: {summary.get('most_accurate_method', 'N/A')}")
            click.echo(f"  Most reliable method: {summary.get('most_reliable_method', 'N/A')}")
        
    except Exception as e:
        logger.error(f"Benchmark failed: {str(e)}")
        click.echo(f"❌ Error: {str(e)}", err=True)
        raise click.Abort()


if __name__ == '__main__':
    main()
