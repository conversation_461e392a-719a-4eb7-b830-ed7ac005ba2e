"""
Command-line interface for data format conversion.

This module provides tools for converting between different data formats
commonly used in biosignal processing.
"""

import click
import numpy as np
import pandas as pd
import h5py
from pathlib import Path
import json
import logging
from typing import Dict, Any


@click.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.argument('output_file', type=click.Path())
@click.option('--input-format', type=click.Choice(['csv', 'hdf5', 'json', 'auto']), 
              default='auto', help='Input file format')
@click.option('--output-format', type=click.Choice(['csv', 'hdf5', 'json']), 
              required=True, help='Output file format')
@click.option('--ppg-column', default='ppg', help='PPG column name')
@click.option('--acc-columns', default='acc_x,acc_y,acc_z', help='Accelerometer column names')
@click.option('--time-column', default='time', help='Time column name')
@click.option('--sampling-rate', type=float, help='Sampling rate (for formats that need it)')
@click.option('--compress', is_flag=True, help='Compress output (for HDF5)')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def main(input_file, output_file, input_format, output_format, ppg_column, 
         acc_columns, time_column, sampling_rate, compress, verbose):
    """
    Convert biosignal data between different formats.
    
    INPUT_FILE: Path to input data file
    OUTPUT_FILE: Path to output data file
    """
    # Setup logging
    logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)
    logger = logging.getLogger(__name__)
    
    try:
        input_path = Path(input_file)
        output_path = Path(output_file)
        
        # Auto-detect input format
        if input_format == 'auto':
            if input_path.suffix.lower() == '.csv':
                input_format = 'csv'
            elif input_path.suffix.lower() in ['.h5', '.hdf5']:
                input_format = 'hdf5'
            elif input_path.suffix.lower() == '.json':
                input_format = 'json'
            else:
                raise ValueError(f"Cannot auto-detect format for {input_path.suffix}")
        
        logger.info(f"Converting {input_format} -> {output_format}")
        logger.info(f"Input: {input_file}")
        logger.info(f"Output: {output_file}")
        
        # Load data based on input format
        data = None
        metadata = {}
        
        if input_format == 'csv':
            logger.info("Loading CSV data")
            data = pd.read_csv(input_file)
            
            # Extract metadata
            metadata = {
                'format': 'csv',
                'columns': list(data.columns),
                'num_samples': len(data),
                'sampling_rate': sampling_rate
            }
            
        elif input_format == 'hdf5':
            logger.info("Loading HDF5 data")
            with h5py.File(input_file, 'r') as f:
                # Try to load common dataset names
                datasets = {}
                for key in f.keys():
                    datasets[key] = f[key][:]
                
                # Convert to DataFrame
                data = pd.DataFrame(datasets)
                
                # Load metadata if available
                if 'metadata' in f.attrs:
                    metadata = dict(f.attrs)
                else:
                    metadata = {
                        'format': 'hdf5',
                        'datasets': list(datasets.keys()),
                        'num_samples': len(data)
                    }
                    
        elif input_format == 'json':
            logger.info("Loading JSON data")
            with open(input_file, 'r') as f:
                json_data = json.load(f)
            
            if 'data' in json_data:
                data = pd.DataFrame(json_data['data'])
                metadata = json_data.get('metadata', {})
            else:
                # Assume the JSON is directly the data
                data = pd.DataFrame(json_data)
                metadata = {'format': 'json'}
        
        if data is None or data.empty:
            raise ValueError("No data loaded from input file")
        
        logger.info(f"Loaded data: {len(data)} samples, {len(data.columns)} columns")
        
        # Validate required columns
        acc_cols = [col.strip() for col in acc_columns.split(',')]
        
        missing_cols = []
        if ppg_column not in data.columns:
            missing_cols.append(ppg_column)
        
        available_acc_cols = [col for col in acc_cols if col in data.columns]
        if len(available_acc_cols) < len(acc_cols):
            logger.warning(f"Some accelerometer columns missing: {set(acc_cols) - set(available_acc_cols)}")
        
        # Create output directory if needed
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save data based on output format
        if output_format == 'csv':
            logger.info("Saving as CSV")
            data.to_csv(output_file, index=False)
            
            # Save metadata separately
            metadata_file = output_path.parent / f"{output_path.stem}_metadata.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
                
        elif output_format == 'hdf5':
            logger.info("Saving as HDF5")
            with h5py.File(output_file, 'w') as f:
                # Save each column as a dataset
                for col in data.columns:
                    if compress:
                        f.create_dataset(col, data=data[col].values, compression='gzip')
                    else:
                        f.create_dataset(col, data=data[col].values)
                
                # Save metadata as attributes
                for key, value in metadata.items():
                    if isinstance(value, (str, int, float, bool)):
                        f.attrs[key] = value
                    else:
                        f.attrs[key] = str(value)
                        
        elif output_format == 'json':
            logger.info("Saving as JSON")
            output_data = {
                'metadata': metadata,
                'data': data.to_dict('records')
            }
            
            with open(output_file, 'w') as f:
                json.dump(output_data, f, indent=2)
        
        # Print conversion summary
        click.echo(f"\n✓ Conversion completed successfully!")
        click.echo(f"  Input format: {input_format}")
        click.echo(f"  Output format: {output_format}")
        click.echo(f"  Samples converted: {len(data)}")
        click.echo(f"  Columns: {list(data.columns)}")
        
        if ppg_column in data.columns:
            click.echo(f"  PPG column: {ppg_column} ✓")
        else:
            click.echo(f"  PPG column: {ppg_column} ❌")
        
        if available_acc_cols:
            click.echo(f"  Accelerometer columns: {available_acc_cols} ✓")
        else:
            click.echo(f"  Accelerometer columns: None found")
        
        # File size comparison
        input_size = input_path.stat().st_size / (1024**2)  # MB
        output_size = output_path.stat().st_size / (1024**2)  # MB
        
        click.echo(f"  Input file size: {input_size:.2f} MB")
        click.echo(f"  Output file size: {output_size:.2f} MB")
        
        if output_size < input_size:
            compression_ratio = (1 - output_size / input_size) * 100
            click.echo(f"  Compression: {compression_ratio:.1f}% reduction")
        elif output_size > input_size:
            expansion_ratio = (output_size / input_size - 1) * 100
            click.echo(f"  Expansion: {expansion_ratio:.1f}% increase")
        
    except Exception as e:
        logger.error(f"Conversion failed: {str(e)}")
        click.echo(f"❌ Error: {str(e)}", err=True)
        raise click.Abort()


if __name__ == '__main__':
    main()
