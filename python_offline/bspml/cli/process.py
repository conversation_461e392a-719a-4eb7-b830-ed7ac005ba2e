"""
Command-line interface for PPG signal processing.

This module provides a CLI for processing PPG signals and estimating heart rate.
"""

import click
import numpy as np
import pandas as pd
from pathlib import Path
import json
import logging

from bspml.preprocessing import preprocess_ppg
from bspml.hr_estimation import estimate_heart_rate


@click.command()
@click.argument('input_file', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--sampling-rate', '-sr', default=64.0, help='Sampling rate in Hz')
@click.option('--ppg-column', default='ppg', help='PPG column name')
@click.option('--acc-columns', default='acc_x,acc_y,acc_z', help='Accelerometer column names (comma-separated)')
@click.option('--no-preprocessing', is_flag=True, help='Skip preprocessing')
@click.option('--no-motion-removal', is_flag=True, help='Skip motion artifact removal')
@click.option('--format', 'output_format', default='csv', type=click.Choice(['csv', 'json']), help='Output format')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
def main(input_file, output, sampling_rate, ppg_column, acc_columns, 
         no_preprocessing, no_motion_removal, output_format, verbose):
    """
    Process PPG signals and estimate heart rate.
    
    INPUT_FILE: Path to input CSV file containing PPG and optional accelerometer data
    """
    # Setup logging
    logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)
    logger = logging.getLogger(__name__)
    
    try:
        # Load data
        logger.info(f"Loading data from {input_file}")
        data = pd.read_csv(input_file)
        
        # Extract PPG signal
        if ppg_column not in data.columns:
            raise ValueError(f"PPG column '{ppg_column}' not found in data")
        
        ppg_signal = data[ppg_column].values
        logger.info(f"Loaded PPG signal with {len(ppg_signal)} samples")
        
        # Extract accelerometer signals if available
        acc_signals = None
        if not no_motion_removal:
            acc_cols = [col.strip() for col in acc_columns.split(',')]
            if all(col in data.columns for col in acc_cols):
                acc_signals = data[acc_cols].values
                logger.info(f"Loaded accelerometer data with {len(acc_cols)} channels")
            else:
                logger.warning("Accelerometer columns not found, skipping motion artifact removal")
        
        # Preprocessing
        if not no_preprocessing:
            logger.info("Preprocessing PPG signal")
            processed_ppg = preprocess_ppg(
                ppg_signal,
                acc_signals=acc_signals,
                sampling_rate=sampling_rate,
                enable_motion_removal=acc_signals is not None
            )
        else:
            processed_ppg = ppg_signal
            logger.info("Skipping preprocessing")
        
        # Heart rate estimation
        logger.info("Estimating heart rate")
        hr_results = estimate_heart_rate(processed_ppg, sampling_rate)
        
        if not hr_results['success']:
            raise ValueError(f"Heart rate estimation failed: {hr_results.get('error', 'Unknown error')}")
        
        # Prepare output
        output_data = {
            'success': hr_results['success'],
            'sampling_rate': sampling_rate,
            'signal_length_seconds': len(ppg_signal) / sampling_rate,
            'time_points': hr_results['time_points'].tolist(),
            'hr_values': hr_results['hr_values'].tolist(),
            'hr_values_smoothed': hr_results['hr_values_smoothed'].tolist(),
            'statistics': hr_results.get('statistics', {}),
            'quality': hr_results.get('quality', {}),
            'processing_parameters': {
                'preprocessing_enabled': not no_preprocessing,
                'motion_removal_enabled': not no_motion_removal and acc_signals is not None,
                'ppg_column': ppg_column,
                'acc_columns': acc_cols if acc_signals is not None else None
            }
        }
        
        # Save output
        if output:
            output_path = Path(output)
        else:
            input_path = Path(input_file)
            output_path = input_path.parent / f"{input_path.stem}_hr_results.{output_format}"
        
        logger.info(f"Saving results to {output_path}")
        
        if output_format == 'json':
            with open(output_path, 'w') as f:
                json.dump(output_data, f, indent=2)
        else:  # CSV
            # Create DataFrame with results
            results_df = pd.DataFrame({
                'time': output_data['time_points'],
                'heart_rate_bpm': output_data['hr_values'],
                'heart_rate_smoothed_bpm': output_data['hr_values_smoothed']
            })
            results_df.to_csv(output_path, index=False)
            
            # Save metadata as separate JSON file
            metadata_path = output_path.parent / f"{output_path.stem}_metadata.json"
            metadata = {k: v for k, v in output_data.items() 
                       if k not in ['time_points', 'hr_values', 'hr_values_smoothed']}
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
        
        # Print summary
        stats = output_data['statistics']
        click.echo(f"\n✓ Processing completed successfully!")
        click.echo(f"  Input file: {input_file}")
        click.echo(f"  Output file: {output_path}")
        click.echo(f"  Signal duration: {output_data['signal_length_seconds']:.1f} seconds")
        click.echo(f"  Heart rate estimates: {len(output_data['hr_values'])}")
        
        if stats:
            click.echo(f"  Mean heart rate: {stats.get('mean_hr_bpm', 0):.1f} BPM")
            click.echo(f"  HR range: {stats.get('min_hr_bpm', 0):.1f} - {stats.get('max_hr_bpm', 0):.1f} BPM")
            click.echo(f"  HR variability (CV): {stats.get('cv_hr', 0):.3f}")
        
        quality = output_data.get('quality', {})
        if quality:
            click.echo(f"  Signal quality: {quality.get('quality_classification', 'unknown')}")
            click.echo(f"  Quality score: {quality.get('overall_quality_score', 0):.3f}")
        
    except Exception as e:
        logger.error(f"Processing failed: {str(e)}")
        click.echo(f"❌ Error: {str(e)}", err=True)
        raise click.Abort()


if __name__ == '__main__':
    main()
