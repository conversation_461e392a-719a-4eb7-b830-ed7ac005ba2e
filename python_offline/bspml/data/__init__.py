"""
Data Handling Module

This module provides utilities for loading and handling PPG and accelerometer
data from various sources including PPG-DaLiA dataset and real-world collections.
"""

from .loaders import load_ppg_dalia, load_real_world_data
from .preprocessing import validate_data, synchronize_signals
from .utils import create_data_summary, export_data

__all__ = [
    "load_ppg_dalia",
    "load_real_world_data",
    "validate_data",
    "synchronize_signals",
    "create_data_summary",
    "export_data"
]
