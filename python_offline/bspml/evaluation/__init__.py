"""
Evaluation Module

This module provides tools for performance evaluation and benchmarking
of PPG preprocessing and heart rate estimation algorithms.
"""

from .metrics import calculate_hr_accuracy, calculate_preprocessing_metrics
from .benchmarking import run_benchmark, compare_algorithms
from .visualization import plot_results, create_evaluation_report

__all__ = [
    "calculate_hr_accuracy",
    "calculate_preprocessing_metrics", 
    "run_benchmark",
    "compare_algorithms",
    "plot_results",
    "create_evaluation_report"
]
