"""
Heart Rate Calculation Module

Converts detected PPG peaks into instantaneous heart rate measurements
and provides statistical analysis of heart rate variability.
"""

import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from scipy import interpolate, signal
import logging

logger = logging.getLogger(__name__)


def calculate_instantaneous_hr(
    peak_indices: np.ndarray,
    sampling_rate: float,
    interpolation_rate: Optional[float] = None,
    method: str = 'linear'
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate instantaneous heart rate from detected peaks.
    
    This function converts peak locations to instantaneous heart rate
    values and optionally interpolates to a regular time grid.
    
    Args:
        peak_indices: Array of peak indices in the signal
        sampling_rate: Original signal sampling rate in Hz
        interpolation_rate: Target interpolation rate in Hz (optional)
        method: Interpolation method ('linear', 'cubic', 'nearest')
        
    Returns:
        Tuple of (time_points, hr_values) in seconds and BPM
        
    Example:
        >>> peaks = np.array([64, 128, 192, ...])  # Peak indices
        >>> time, hr = calculate_instantaneous_hr(peaks, sampling_rate=64)
    """
    if len(peak_indices) < 2:
        raise ValueError("Need at least 2 peaks to calculate heart rate")
    
    # Calculate inter-beat intervals (IBI) in seconds
    ibi_samples = np.diff(peak_indices)
    ibi_seconds = ibi_samples / sampling_rate
    
    # Convert IBI to heart rate in BPM
    hr_bpm = 60.0 / ibi_seconds
    
    # Time points for heart rate values (at peak locations)
    peak_times = peak_indices[1:] / sampling_rate  # Skip first peak
    
    # Filter physiologically plausible heart rates
    valid_mask = (hr_bpm >= 30) & (hr_bpm <= 200)
    valid_times = peak_times[valid_mask]
    valid_hr = hr_bpm[valid_mask]
    
    if len(valid_hr) == 0:
        raise ValueError("No valid heart rate values found")
    
    # Interpolate to regular time grid if requested
    if interpolation_rate is not None:
        time_start = valid_times[0]
        time_end = valid_times[-1]
        
        # Create regular time grid
        regular_times = np.arange(time_start, time_end, 1.0 / interpolation_rate)
        
        if len(regular_times) > 1:
            # Interpolate heart rate values
            if method == 'linear':
                interp_func = interpolate.interp1d(
                    valid_times, valid_hr, kind='linear', 
                    bounds_error=False, fill_value='extrapolate'
                )
            elif method == 'cubic':
                if len(valid_hr) >= 4:  # Need at least 4 points for cubic
                    interp_func = interpolate.interp1d(
                        valid_times, valid_hr, kind='cubic',
                        bounds_error=False, fill_value='extrapolate'
                    )
                else:
                    # Fall back to linear if not enough points
                    interp_func = interpolate.interp1d(
                        valid_times, valid_hr, kind='linear',
                        bounds_error=False, fill_value='extrapolate'
                    )
            elif method == 'nearest':
                interp_func = interpolate.interp1d(
                    valid_times, valid_hr, kind='nearest',
                    bounds_error=False, fill_value='extrapolate'
                )
            else:
                raise ValueError(f"Unknown interpolation method: {method}")
            
            interpolated_hr = interp_func(regular_times)
            
            # Ensure interpolated values are within reasonable range
            interpolated_hr = np.clip(interpolated_hr, 30, 200)
            
            return regular_times, interpolated_hr
    
    return valid_times, valid_hr


def calculate_hr_statistics(
    hr_values: np.ndarray,
    time_points: Optional[np.ndarray] = None
) -> Dict[str, float]:
    """
    Calculate comprehensive heart rate statistics.
    
    Args:
        hr_values: Heart rate values in BPM
        time_points: Time points corresponding to HR values (optional)
        
    Returns:
        Dictionary containing HR statistics
    """
    if len(hr_values) == 0:
        return {}
    
    # Basic statistics
    stats = {
        'mean_hr_bpm': float(np.mean(hr_values)),
        'median_hr_bpm': float(np.median(hr_values)),
        'std_hr_bpm': float(np.std(hr_values)),
        'min_hr_bpm': float(np.min(hr_values)),
        'max_hr_bpm': float(np.max(hr_values)),
        'range_hr_bpm': float(np.ptp(hr_values)),
        'cv_hr': float(np.std(hr_values) / np.mean(hr_values)) if np.mean(hr_values) > 0 else 0
    }
    
    # Percentiles
    percentiles = [5, 25, 75, 95]
    for p in percentiles:
        stats[f'hr_p{p}_bpm'] = float(np.percentile(hr_values, p))
    
    # Heart rate variability metrics (if time points available)
    if time_points is not None and len(time_points) == len(hr_values):
        # Calculate RMSSD (root mean square of successive differences)
        hr_diffs = np.diff(hr_values)
        stats['rmssd_bpm'] = float(np.sqrt(np.mean(hr_diffs**2)))
        
        # Calculate pNN50 (percentage of successive RR intervals that differ by > 50ms)
        # Convert HR differences to RR interval differences (approximate)
        rr_diffs_ms = np.abs(hr_diffs) * 1000 / (hr_values[:-1]**2 / 3600)  # Approximate conversion
        stats['pnn50_percent'] = float(np.sum(rr_diffs_ms > 50) / len(rr_diffs_ms) * 100)
        
        # Frequency domain analysis (if enough data)
        if len(hr_values) >= 64:  # Need sufficient data for frequency analysis
            freq_stats = calculate_hrv_frequency_domain(hr_values, time_points)
            stats.update(freq_stats)
    
    return stats


def calculate_hrv_frequency_domain(
    hr_values: np.ndarray,
    time_points: np.ndarray,
    interpolation_rate: float = 4.0
) -> Dict[str, float]:
    """
    Calculate frequency domain heart rate variability metrics.
    
    Args:
        hr_values: Heart rate values in BPM
        time_points: Time points in seconds
        interpolation_rate: Interpolation rate for frequency analysis
        
    Returns:
        Dictionary containing frequency domain HRV metrics
    """
    # Interpolate to regular sampling rate
    time_regular = np.arange(time_points[0], time_points[-1], 1.0 / interpolation_rate)
    
    if len(time_regular) < 64:  # Need sufficient data
        return {}
    
    # Interpolate HR values
    interp_func = interpolate.interp1d(
        time_points, hr_values, kind='linear',
        bounds_error=False, fill_value='extrapolate'
    )
    hr_regular = interp_func(time_regular)
    
    # Remove mean (detrend)
    hr_detrended = hr_regular - np.mean(hr_regular)
    
    # Calculate power spectral density
    freqs, psd = signal.welch(
        hr_detrended,
        fs=interpolation_rate,
        nperseg=min(256, len(hr_detrended) // 4),
        noverlap=None
    )
    
    # Define frequency bands for HRV analysis
    vlf_band = (0.0033, 0.04)  # Very low frequency
    lf_band = (0.04, 0.15)     # Low frequency
    hf_band = (0.15, 0.4)      # High frequency
    
    # Calculate power in each band
    vlf_power = np.trapz(psd[(freqs >= vlf_band[0]) & (freqs < vlf_band[1])], 
                         freqs[(freqs >= vlf_band[0]) & (freqs < vlf_band[1])])
    lf_power = np.trapz(psd[(freqs >= lf_band[0]) & (freqs < lf_band[1])], 
                        freqs[(freqs >= lf_band[0]) & (freqs < lf_band[1])])
    hf_power = np.trapz(psd[(freqs >= hf_band[0]) & (freqs < hf_band[1])], 
                        freqs[(freqs >= hf_band[0]) & (freqs < hf_band[1])])
    
    total_power = vlf_power + lf_power + hf_power
    
    # Calculate ratios
    lf_hf_ratio = lf_power / hf_power if hf_power > 0 else 0
    lf_nu = lf_power / (lf_power + hf_power) * 100 if (lf_power + hf_power) > 0 else 0
    hf_nu = hf_power / (lf_power + hf_power) * 100 if (lf_power + hf_power) > 0 else 0
    
    return {
        'vlf_power': float(vlf_power),
        'lf_power': float(lf_power),
        'hf_power': float(hf_power),
        'total_power': float(total_power),
        'lf_hf_ratio': float(lf_hf_ratio),
        'lf_nu': float(lf_nu),
        'hf_nu': float(hf_nu)
    }


def smooth_hr_signal(
    hr_values: np.ndarray,
    time_points: np.ndarray,
    smoothing_window: float = 5.0,
    method: str = 'moving_average'
) -> np.ndarray:
    """
    Smooth heart rate signal to reduce noise and artifacts.
    
    Args:
        hr_values: Heart rate values in BPM
        time_points: Time points in seconds
        smoothing_window: Smoothing window size in seconds
        method: Smoothing method ('moving_average', 'gaussian', 'median')
        
    Returns:
        Smoothed heart rate values
    """
    if len(hr_values) != len(time_points):
        raise ValueError("HR values and time points must have same length")
    
    if len(hr_values) < 3:
        return hr_values.copy()
    
    # Calculate sampling rate
    dt = np.mean(np.diff(time_points))
    window_samples = int(smoothing_window / dt)
    window_samples = max(3, min(window_samples, len(hr_values) // 2))
    
    if method == 'moving_average':
        # Simple moving average
        smoothed = np.convolve(hr_values, np.ones(window_samples) / window_samples, mode='same')
        
    elif method == 'gaussian':
        # Gaussian smoothing
        sigma = window_samples / 6  # 6-sigma window
        smoothed = signal.gaussian_filter1d(hr_values, sigma)
        
    elif method == 'median':
        # Median filter (good for removing outliers)
        smoothed = signal.medfilt(hr_values, kernel_size=window_samples if window_samples % 2 == 1 else window_samples + 1)
        
    else:
        raise ValueError(f"Unknown smoothing method: {method}")
    
    return smoothed


def detect_hr_anomalies(
    hr_values: np.ndarray,
    time_points: np.ndarray,
    threshold_std: float = 2.5,
    min_duration: float = 2.0
) -> Dict[str, Any]:
    """
    Detect anomalies in heart rate signal.
    
    Args:
        hr_values: Heart rate values in BPM
        time_points: Time points in seconds
        threshold_std: Threshold in standard deviations for anomaly detection
        min_duration: Minimum duration for sustained anomalies in seconds
        
    Returns:
        Dictionary containing anomaly information
    """
    if len(hr_values) < 10:
        return {'anomalies': [], 'num_anomalies': 0}
    
    # Calculate rolling statistics
    window_size = min(20, len(hr_values) // 4)
    rolling_mean = np.convolve(hr_values, np.ones(window_size) / window_size, mode='same')
    rolling_std = np.array([
        np.std(hr_values[max(0, i - window_size//2):min(len(hr_values), i + window_size//2)])
        for i in range(len(hr_values))
    ])
    
    # Detect outliers
    z_scores = np.abs(hr_values - rolling_mean) / (rolling_std + 1e-6)
    anomaly_mask = z_scores > threshold_std
    
    # Find sustained anomalies
    anomalies = []
    in_anomaly = False
    anomaly_start = 0
    
    for i, is_anomaly in enumerate(anomaly_mask):
        if is_anomaly and not in_anomaly:
            # Start of anomaly
            in_anomaly = True
            anomaly_start = i
        elif not is_anomaly and in_anomaly:
            # End of anomaly
            in_anomaly = False
            duration = time_points[i-1] - time_points[anomaly_start]
            
            if duration >= min_duration:
                anomalies.append({
                    'start_time': time_points[anomaly_start],
                    'end_time': time_points[i-1],
                    'duration': duration,
                    'start_index': anomaly_start,
                    'end_index': i-1,
                    'mean_hr': np.mean(hr_values[anomaly_start:i]),
                    'max_z_score': np.max(z_scores[anomaly_start:i])
                })
    
    # Handle case where signal ends during anomaly
    if in_anomaly:
        duration = time_points[-1] - time_points[anomaly_start]
        if duration >= min_duration:
            anomalies.append({
                'start_time': time_points[anomaly_start],
                'end_time': time_points[-1],
                'duration': duration,
                'start_index': anomaly_start,
                'end_index': len(hr_values) - 1,
                'mean_hr': np.mean(hr_values[anomaly_start:]),
                'max_z_score': np.max(z_scores[anomaly_start:])
            })
    
    return {
        'anomalies': anomalies,
        'num_anomalies': len(anomalies),
        'anomaly_ratio': np.sum(anomaly_mask) / len(anomaly_mask),
        'z_scores': z_scores
    }
