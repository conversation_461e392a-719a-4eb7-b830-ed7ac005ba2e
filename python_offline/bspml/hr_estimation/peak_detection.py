"""
Peak Detection Module

Implements local maxima detection in sliding windows for PPG peak identification.
This is the core component for heart rate estimation from PPG signals.
"""

import numpy as np
from typing import List, Tuple, Optional, Dict, Any
from scipy import signal
import logging

logger = logging.getLogger(__name__)


def find_peaks_sliding_window(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    window_size: float = 10.0,
    overlap: float = 0.5,
    min_peak_distance: float = 0.3,
    min_peak_height: Optional[float] = None,
    adaptive_threshold: bool = True
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Find PPG peaks using sliding window approach with local maxima detection.
    
    This function divides the signal into overlapping windows and detects
    local maxima within each window, ensuring physiologically plausible
    peak spacing for heart rate estimation.
    
    Args:
        ppg_signal: Input PPG signal array
        sampling_rate: Sampling rate in Hz
        window_size: Window size in seconds (default: 10.0)
        overlap: Window overlap ratio (0-1, default: 0.5)
        min_peak_distance: Minimum distance between peaks in seconds (default: 0.3)
        min_peak_height: Minimum peak height (default: adaptive)
        adaptive_threshold: Whether to use adaptive thresholding
        
    Returns:
        Tuple of (peak_indices, peak_values)
        
    Example:
        >>> ppg = np.array([...])  # Preprocessed PPG signal
        >>> peaks, values = find_peaks_sliding_window(ppg, sampling_rate=64)
    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")
    
    if sampling_rate <= 0:
        raise ValueError("Sampling rate must be positive")
    
    # Convert time parameters to samples
    window_samples = int(window_size * sampling_rate)
    step_samples = int(window_samples * (1 - overlap))
    min_distance_samples = int(min_peak_distance * sampling_rate)
    
    # Initialize peak storage
    all_peaks = []
    all_peak_values = []
    
    # Process signal in sliding windows
    for start_idx in range(0, len(ppg_signal) - window_samples + 1, step_samples):
        end_idx = start_idx + window_samples
        window_signal = ppg_signal[start_idx:end_idx]
        
        # Find peaks in current window
        window_peaks = detect_peaks_in_window(
            window_signal,
            min_distance_samples,
            min_peak_height,
            adaptive_threshold
        )
        
        # Convert local indices to global indices
        global_peaks = window_peaks + start_idx
        
        # Store peaks and their values
        for peak_idx in global_peaks:
            if peak_idx < len(ppg_signal):
                all_peaks.append(peak_idx)
                all_peak_values.append(ppg_signal[peak_idx])
    
    # Remove duplicate peaks from overlapping windows
    if all_peaks:
        peaks_array = np.array(all_peaks)
        values_array = np.array(all_peak_values)
        
        # Sort by peak index
        sort_indices = np.argsort(peaks_array)
        peaks_array = peaks_array[sort_indices]
        values_array = values_array[sort_indices]
        
        # Remove duplicates within minimum distance
        unique_peaks, unique_values = remove_duplicate_peaks(
            peaks_array, values_array, min_distance_samples
        )
        
        return unique_peaks, unique_values
    else:
        return np.array([]), np.array([])


def detect_peaks_in_window(
    window_signal: np.ndarray,
    min_distance: int,
    min_height: Optional[float] = None,
    adaptive_threshold: bool = True
) -> np.ndarray:
    """
    Detect peaks within a single window using local maxima.
    
    Args:
        window_signal: Signal window
        min_distance: Minimum distance between peaks in samples
        min_height: Minimum peak height
        adaptive_threshold: Whether to use adaptive thresholding
        
    Returns:
        Array of peak indices within the window
    """
    if len(window_signal) < 3:
        return np.array([])
    
    # Calculate adaptive threshold if requested
    if adaptive_threshold:
        if min_height is None:
            # Use median + fraction of signal range as threshold
            signal_median = np.median(window_signal)
            signal_range = np.ptp(window_signal)  # peak-to-peak
            min_height = signal_median + 0.1 * signal_range
    
    # Find peaks using scipy
    peaks, properties = signal.find_peaks(
        window_signal,
        height=min_height,
        distance=min_distance
    )
    
    return peaks


def detect_ppg_peaks(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    method: str = 'sliding_window',
    **kwargs
) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Detect PPG peaks using specified method.
    
    Args:
        ppg_signal: Input PPG signal
        sampling_rate: Sampling rate in Hz
        method: Peak detection method ('sliding_window', 'adaptive', 'template')
        **kwargs: Additional parameters for the chosen method
        
    Returns:
        Tuple of (peak_indices, detection_info)
    """
    if method == 'sliding_window':
        peaks, values = find_peaks_sliding_window(ppg_signal, sampling_rate, **kwargs)
        detection_info = {
            'method': method,
            'num_peaks': len(peaks),
            'peak_values': values,
            'parameters': kwargs
        }
        
    elif method == 'adaptive':
        peaks, values = adaptive_peak_detection(ppg_signal, sampling_rate, **kwargs)
        detection_info = {
            'method': method,
            'num_peaks': len(peaks),
            'peak_values': values,
            'parameters': kwargs
        }
        
    elif method == 'template':
        peaks, values = template_matching_peaks(ppg_signal, sampling_rate, **kwargs)
        detection_info = {
            'method': method,
            'num_peaks': len(peaks),
            'peak_values': values,
            'parameters': kwargs
        }
    else:
        raise ValueError(f"Unknown peak detection method: {method}")
    
    return peaks, detection_info


def adaptive_peak_detection(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    adaptation_window: float = 5.0,
    threshold_factor: float = 0.6
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Adaptive peak detection with dynamic thresholding.
    
    Args:
        ppg_signal: Input PPG signal
        sampling_rate: Sampling rate in Hz
        adaptation_window: Window size for threshold adaptation in seconds
        threshold_factor: Factor for threshold calculation (0-1)
        
    Returns:
        Tuple of (peak_indices, peak_values)
    """
    window_samples = int(adaptation_window * sampling_rate)
    peaks = []
    values = []
    
    for i in range(len(ppg_signal)):
        # Define adaptation window around current sample
        start_idx = max(0, i - window_samples // 2)
        end_idx = min(len(ppg_signal), i + window_samples // 2)
        
        local_window = ppg_signal[start_idx:end_idx]
        
        # Calculate adaptive threshold
        local_max = np.max(local_window)
        local_min = np.min(local_window)
        threshold = local_min + threshold_factor * (local_max - local_min)
        
        # Check if current sample is a peak
        if (i > 0 and i < len(ppg_signal) - 1 and
            ppg_signal[i] > ppg_signal[i-1] and
            ppg_signal[i] > ppg_signal[i+1] and
            ppg_signal[i] > threshold):
            
            # Check minimum distance constraint
            if not peaks or i - peaks[-1] >= int(0.3 * sampling_rate):
                peaks.append(i)
                values.append(ppg_signal[i])
    
    return np.array(peaks), np.array(values)


def template_matching_peaks(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    template_length: float = 1.0,
    correlation_threshold: float = 0.7
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Peak detection using template matching approach.
    
    Args:
        ppg_signal: Input PPG signal
        sampling_rate: Sampling rate in Hz
        template_length: Template length in seconds
        correlation_threshold: Minimum correlation for peak detection
        
    Returns:
        Tuple of (peak_indices, peak_values)
    """
    # Create a generic PPG pulse template
    template_samples = int(template_length * sampling_rate)
    t = np.linspace(0, 1, template_samples)
    
    # Simple PPG pulse template (systolic peak + diastolic notch)
    template = np.exp(-2 * t) * np.sin(2 * np.pi * t) + 0.3 * np.exp(-5 * t) * np.sin(4 * np.pi * t)
    template = template / np.max(template)  # Normalize
    
    # Perform template matching
    correlation = np.correlate(ppg_signal, template, mode='valid')
    
    # Find peaks in correlation signal
    corr_peaks, _ = signal.find_peaks(
        correlation,
        height=correlation_threshold * np.max(correlation),
        distance=int(0.3 * sampling_rate)
    )
    
    # Convert correlation peaks to signal peaks
    signal_peaks = corr_peaks + len(template) // 2
    peak_values = ppg_signal[signal_peaks]
    
    return signal_peaks, peak_values


def remove_duplicate_peaks(
    peaks: np.ndarray,
    values: np.ndarray,
    min_distance: int
) -> Tuple[np.ndarray, np.ndarray]:
    """
    Remove duplicate peaks that are too close together.
    
    When peaks are within minimum distance, keep the one with higher value.
    
    Args:
        peaks: Array of peak indices
        values: Array of peak values
        min_distance: Minimum distance between peaks
        
    Returns:
        Tuple of (unique_peaks, unique_values)
    """
    if len(peaks) <= 1:
        return peaks, values
    
    unique_peaks = []
    unique_values = []
    
    i = 0
    while i < len(peaks):
        current_peak = peaks[i]
        current_value = values[i]
        
        # Find all peaks within minimum distance
        j = i + 1
        while j < len(peaks) and peaks[j] - current_peak < min_distance:
            # Keep the peak with higher value
            if values[j] > current_value:
                current_peak = peaks[j]
                current_value = values[j]
            j += 1
        
        unique_peaks.append(current_peak)
        unique_values.append(current_value)
        
        i = j
    
    return np.array(unique_peaks), np.array(unique_values)


def validate_peak_detection(
    peaks: np.ndarray,
    ppg_signal: np.ndarray,
    sampling_rate: float
) -> Dict[str, Any]:
    """
    Validate the quality of peak detection.
    
    Args:
        peaks: Detected peak indices
        ppg_signal: Original PPG signal
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Dictionary containing validation metrics
    """
    if len(peaks) < 2:
        return {
            'num_peaks': len(peaks),
            'mean_hr_bpm': 0,
            'hr_variability': 0,
            'detection_quality': 'insufficient_peaks'
        }
    
    # Calculate inter-beat intervals
    ibi = np.diff(peaks) / sampling_rate  # in seconds
    
    # Calculate heart rate
    hr_bpm = 60.0 / ibi
    
    # Filter physiologically plausible heart rates (30-200 BPM)
    valid_hr_mask = (hr_bpm >= 30) & (hr_bpm <= 200)
    valid_hr = hr_bpm[valid_hr_mask]
    
    if len(valid_hr) == 0:
        return {
            'num_peaks': len(peaks),
            'mean_hr_bpm': 0,
            'hr_variability': 0,
            'detection_quality': 'no_valid_hr'
        }
    
    # Calculate statistics
    mean_hr = np.mean(valid_hr)
    hr_std = np.std(valid_hr)
    hr_cv = hr_std / mean_hr if mean_hr > 0 else 0  # Coefficient of variation
    
    # Assess detection quality
    valid_ratio = len(valid_hr) / len(hr_bpm)
    
    if valid_ratio > 0.8 and hr_cv < 0.3:
        quality = 'good'
    elif valid_ratio > 0.6 and hr_cv < 0.5:
        quality = 'fair'
    else:
        quality = 'poor'
    
    return {
        'num_peaks': len(peaks),
        'num_valid_intervals': len(valid_hr),
        'valid_ratio': valid_ratio,
        'mean_hr_bpm': mean_hr,
        'hr_std_bpm': hr_std,
        'hr_variability_cv': hr_cv,
        'detection_quality': quality,
        'hr_range_bpm': (np.min(valid_hr), np.max(valid_hr)) if len(valid_hr) > 0 else (0, 0)
    }
