"""
Heart Rate Estimation Pipeline

Combines peak detection and heart rate calculation into a unified pipeline
for extracting instantaneous heart rate from preprocessed PPG signals.
"""

import numpy as np
from typing import Optional, Dict, Any, Tuple
import logging

from .peak_detection import detect_ppg_peaks, validate_peak_detection
from .hr_calculation import (
    calculate_instantaneous_hr, 
    calculate_hr_statistics,
    smooth_hr_signal,
    detect_hr_anomalies
)

logger = logging.getLogger(__name__)


def estimate_heart_rate(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    peak_detection_method: str = 'sliding_window',
    interpolation_rate: Optional[float] = 1.0,
    smoothing_window: Optional[float] = None,
    return_peaks: bool = False,
    return_statistics: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Complete heart rate estimation pipeline from preprocessed PPG signal.
    
    This function performs the complete workflow:
    1. Peak detection using specified method
    2. Heart rate calculation from peaks
    3. Optional smoothing and interpolation
    4. Statistical analysis and quality assessment
    
    Args:
        ppg_signal: Preprocessed PPG signal array
        sampling_rate: Sampling rate in Hz
        peak_detection_method: Method for peak detection
        interpolation_rate: Target interpolation rate in Hz (None to disable)
        smoothing_window: Smoothing window size in seconds (None to disable)
        return_peaks: Whether to include peak information in results
        return_statistics: Whether to calculate HR statistics
        **kwargs: Additional parameters for peak detection
        
    Returns:
        Dictionary containing heart rate estimation results
        
    Example:
        >>> ppg_clean = preprocess_ppg(raw_ppg, acc_data, sampling_rate=64)
        >>> hr_results = estimate_heart_rate(ppg_clean, sampling_rate=64)
        >>> print(f"Mean HR: {hr_results['statistics']['mean_hr_bpm']:.1f} BPM")
    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")
    
    if sampling_rate <= 0:
        raise ValueError("Sampling rate must be positive")
    
    logger.info(f"Starting heart rate estimation for {len(ppg_signal)} samples")
    
    # Step 1: Peak Detection
    logger.info(f"Detecting peaks using {peak_detection_method} method")
    
    try:
        peaks, peak_info = detect_ppg_peaks(
            ppg_signal, 
            sampling_rate, 
            method=peak_detection_method,
            **kwargs
        )
        
        if len(peaks) < 2:
            logger.warning("Insufficient peaks detected for heart rate calculation")
            return {
                'success': False,
                'error': 'insufficient_peaks',
                'num_peaks': len(peaks),
                'signal_length_seconds': len(ppg_signal) / sampling_rate
            }
        
        logger.info(f"Detected {len(peaks)} peaks")
        
    except Exception as e:
        logger.error(f"Peak detection failed: {str(e)}")
        return {
            'success': False,
            'error': 'peak_detection_failed',
            'error_message': str(e)
        }
    
    # Step 2: Heart Rate Calculation
    logger.info("Calculating instantaneous heart rate")
    
    try:
        time_points, hr_values = calculate_instantaneous_hr(
            peaks, 
            sampling_rate,
            interpolation_rate=interpolation_rate,
            method='linear'
        )
        
        logger.info(f"Calculated HR for {len(hr_values)} time points")
        
    except Exception as e:
        logger.error(f"Heart rate calculation failed: {str(e)}")
        return {
            'success': False,
            'error': 'hr_calculation_failed',
            'error_message': str(e),
            'num_peaks': len(peaks)
        }
    
    # Step 3: Optional Smoothing
    if smoothing_window is not None and smoothing_window > 0:
        logger.info(f"Applying smoothing with {smoothing_window}s window")
        hr_values_smoothed = smooth_hr_signal(
            hr_values, 
            time_points, 
            smoothing_window=smoothing_window,
            method='moving_average'
        )
    else:
        hr_values_smoothed = hr_values.copy()
    
    # Prepare results
    results = {
        'success': True,
        'time_points': time_points,
        'hr_values': hr_values,
        'hr_values_smoothed': hr_values_smoothed,
        'sampling_rate': sampling_rate,
        'signal_length_seconds': len(ppg_signal) / sampling_rate,
        'estimation_parameters': {
            'peak_detection_method': peak_detection_method,
            'interpolation_rate': interpolation_rate,
            'smoothing_window': smoothing_window,
            **kwargs
        }
    }
    
    # Step 4: Peak Information (if requested)
    if return_peaks:
        peak_validation = validate_peak_detection(peaks, ppg_signal, sampling_rate)
        results['peaks'] = {
            'indices': peaks,
            'values': ppg_signal[peaks],
            'times': peaks / sampling_rate,
            'validation': peak_validation,
            'detection_info': peak_info
        }
    
    # Step 5: Statistical Analysis (if requested)
    if return_statistics:
        logger.info("Calculating heart rate statistics")
        
        # Basic statistics
        hr_stats = calculate_hr_statistics(hr_values_smoothed, time_points)
        results['statistics'] = hr_stats
        
        # Anomaly detection
        anomaly_info = detect_hr_anomalies(hr_values_smoothed, time_points)
        results['anomalies'] = anomaly_info
        
        # Quality assessment
        quality_metrics = assess_hr_quality(
            hr_values_smoothed, 
            time_points, 
            ppg_signal, 
            sampling_rate
        )
        results['quality'] = quality_metrics
    
    logger.info("Heart rate estimation completed successfully")
    
    return results


def estimate_heart_rate_realtime(
    ppg_buffer: np.ndarray,
    sampling_rate: float,
    buffer_length: float = 10.0,
    update_interval: float = 1.0,
    **kwargs
) -> Optional[float]:
    """
    Real-time heart rate estimation from a sliding buffer.
    
    This function is designed for real-time applications where PPG data
    arrives continuously and heart rate needs to be updated periodically.
    
    Args:
        ppg_buffer: Circular buffer containing recent PPG samples
        sampling_rate: Sampling rate in Hz
        buffer_length: Buffer length in seconds
        update_interval: Update interval in seconds
        **kwargs: Additional parameters for heart rate estimation
        
    Returns:
        Current heart rate estimate in BPM (None if insufficient data)
    """
    required_samples = int(buffer_length * sampling_rate)
    
    if len(ppg_buffer) < required_samples:
        return None
    
    # Use most recent data for estimation
    recent_data = ppg_buffer[-required_samples:]
    
    try:
        # Estimate heart rate using standard pipeline
        hr_results = estimate_heart_rate(
            recent_data,
            sampling_rate,
            interpolation_rate=None,  # No interpolation for real-time
            return_peaks=False,
            return_statistics=False,
            **kwargs
        )
        
        if hr_results['success'] and len(hr_results['hr_values']) > 0:
            # Return most recent heart rate estimate
            return float(hr_results['hr_values'][-1])
        else:
            return None
            
    except Exception as e:
        logger.warning(f"Real-time HR estimation failed: {str(e)}")
        return None


def assess_hr_quality(
    hr_values: np.ndarray,
    time_points: np.ndarray,
    ppg_signal: np.ndarray,
    sampling_rate: float
) -> Dict[str, Any]:
    """
    Assess the quality of heart rate estimation.
    
    Args:
        hr_values: Estimated heart rate values
        time_points: Time points for HR values
        ppg_signal: Original PPG signal
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Dictionary containing quality metrics
    """
    quality_metrics = {}
    
    # Basic quality indicators
    quality_metrics['num_hr_values'] = len(hr_values)
    quality_metrics['estimation_duration'] = time_points[-1] - time_points[0] if len(time_points) > 1 else 0
    quality_metrics['mean_hr_bpm'] = float(np.mean(hr_values))
    quality_metrics['hr_variability_cv'] = float(np.std(hr_values) / np.mean(hr_values)) if np.mean(hr_values) > 0 else 0
    
    # Physiological plausibility
    valid_hr_mask = (hr_values >= 30) & (hr_values <= 200)
    quality_metrics['valid_hr_ratio'] = float(np.sum(valid_hr_mask) / len(hr_values))
    
    # Stability assessment
    if len(hr_values) > 1:
        hr_changes = np.abs(np.diff(hr_values))
        quality_metrics['mean_hr_change_bpm'] = float(np.mean(hr_changes))
        quality_metrics['max_hr_change_bpm'] = float(np.max(hr_changes))
        
        # Count large changes (>20 BPM between consecutive estimates)
        large_changes = np.sum(hr_changes > 20)
        quality_metrics['large_changes_ratio'] = float(large_changes / len(hr_changes))
    
    # Signal quality indicators
    signal_stats = {
        'signal_length_seconds': len(ppg_signal) / sampling_rate,
        'signal_rms': float(np.sqrt(np.mean(ppg_signal**2))),
        'signal_snr_estimate': estimate_signal_snr(ppg_signal, sampling_rate)
    }
    quality_metrics.update(signal_stats)
    
    # Overall quality score (0-1, higher is better)
    quality_score = calculate_overall_quality_score(quality_metrics)
    quality_metrics['overall_quality_score'] = quality_score
    
    # Quality classification
    if quality_score > 0.8:
        quality_class = 'excellent'
    elif quality_score > 0.6:
        quality_class = 'good'
    elif quality_score > 0.4:
        quality_class = 'fair'
    else:
        quality_class = 'poor'
    
    quality_metrics['quality_classification'] = quality_class
    
    return quality_metrics


def estimate_signal_snr(ppg_signal: np.ndarray, sampling_rate: float) -> float:
    """
    Estimate signal-to-noise ratio of PPG signal.
    
    Args:
        ppg_signal: PPG signal
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Estimated SNR in dB
    """
    from scipy import signal
    
    # Calculate power spectral density
    freqs, psd = signal.welch(ppg_signal, sampling_rate)
    
    # PPG signal band (0.5-4 Hz)
    signal_mask = (freqs >= 0.5) & (freqs <= 4.0)
    noise_mask = ~signal_mask
    
    signal_power = np.sum(psd[signal_mask])
    noise_power = np.sum(psd[noise_mask])
    
    if noise_power > 0:
        snr_db = 10 * np.log10(signal_power / noise_power)
    else:
        snr_db = float('inf')
    
    return float(snr_db)


def calculate_overall_quality_score(quality_metrics: Dict[str, Any]) -> float:
    """
    Calculate overall quality score from individual metrics.
    
    Args:
        quality_metrics: Dictionary of quality metrics
        
    Returns:
        Overall quality score (0-1)
    """
    score = 0.0
    
    # Valid HR ratio (30% weight)
    valid_ratio = quality_metrics.get('valid_hr_ratio', 0)
    score += 0.3 * valid_ratio
    
    # HR variability (20% weight) - moderate variability is good
    hr_cv = quality_metrics.get('hr_variability_cv', 1.0)
    cv_score = max(0, 1 - abs(hr_cv - 0.1) / 0.2)  # Optimal CV around 0.1
    score += 0.2 * cv_score
    
    # Stability (25% weight) - fewer large changes is better
    large_changes_ratio = quality_metrics.get('large_changes_ratio', 1.0)
    stability_score = max(0, 1 - large_changes_ratio)
    score += 0.25 * stability_score
    
    # Signal SNR (25% weight)
    snr = quality_metrics.get('signal_snr_estimate', 0)
    snr_score = min(1.0, max(0, (snr + 10) / 30))  # Normalize SNR to 0-1
    score += 0.25 * snr_score
    
    return min(1.0, max(0.0, score))
