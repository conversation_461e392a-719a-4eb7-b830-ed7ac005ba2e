"""
Heart Rate Estimation Pipeline

Combines peak detection and heart rate calculation into a unified pipeline
for extracting instantaneous heart rate from preprocessed PPG signals.
"""

import numpy as np
from typing import Optional, Dict, Any, Tuple
import logging

from .peak_detection import detect_ppg_peaks
from .hr_calculation import calculate_instantaneous_hr, calculate_hr_statistics

logger = logging.getLogger(__name__)


def estimate_heart_rate(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    peak_detection_method: str = 'sliding_window',
    interpolation_rate: Optional[float] = 1.0,
    return_peaks: bool = False,
    return_statistics: bool = True,
    **kwargs
) -> Dict[str, Any]:
    """
    Complete heart rate estimation pipeline from preprocessed PPG signal.
    
    This function performs the complete workflow:
    1. Peak detection using specified method
    2. Heart rate calculation from peaks
    3. Optional smoothing and interpolation
    4. Statistical analysis and quality assessment
    
    Args:
        ppg_signal: Preprocessed PPG signal array
        sampling_rate: Sampling rate in Hz
        peak_detection_method: Method for peak detection
        interpolation_rate: Target interpolation rate in Hz (None to disable)
        return_peaks: Whether to include peak information in results
        return_statistics: Whether to calculate HR statistics
        **kwargs: Additional parameters for peak detection
        
    Returns:
        Dictionary containing heart rate estimation results
        
    Example:
        >>> ppg_clean = preprocess_ppg(raw_ppg, acc_data, sampling_rate=64)
        >>> hr_results = estimate_heart_rate(ppg_clean, sampling_rate=64)
        >>> print(f"Mean HR: {hr_results['statistics']['mean_hr_bpm']:.1f} BPM")
    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")
    
    if sampling_rate <= 0:
        raise ValueError("Sampling rate must be positive")
    
    logger.info(f"Starting heart rate estimation for {len(ppg_signal)} samples")
    
    # Step 1: Peak Detection
    logger.info(f"Detecting peaks using {peak_detection_method} method")
    
    try:
        peaks, peak_info = detect_ppg_peaks(
            ppg_signal, 
            sampling_rate, 
            method=peak_detection_method,
            **kwargs
        )
        
        if len(peaks) < 2:
            logger.warning("Insufficient peaks detected for heart rate calculation")
            return {
                'success': False,
                'error': 'insufficient_peaks',
                'num_peaks': len(peaks),
                'signal_length_seconds': len(ppg_signal) / sampling_rate
            }
        
        logger.info(f"Detected {len(peaks)} peaks")
        
    except Exception as e:
        logger.error(f"Peak detection failed: {str(e)}")
        return {
            'success': False,
            'error': 'peak_detection_failed',
            'error_message': str(e)
        }
    
    # Step 2: Heart Rate Calculation
    logger.info("Calculating instantaneous heart rate")
    
    try:
        time_points, hr_values = calculate_instantaneous_hr(
            peaks, 
            sampling_rate,
            interpolation_rate=interpolation_rate,
            method='linear'
        )
        
        logger.info(f"Calculated HR for {len(hr_values)} time points")
        
    except Exception as e:
        logger.error(f"Heart rate calculation failed: {str(e)}")
        return {
            'success': False,
            'error': 'hr_calculation_failed',
            'error_message': str(e),
            'num_peaks': len(peaks)
        }
    
    # No smoothing in simplified version
    hr_values_smoothed = hr_values.copy()
    
    # Prepare results
    results = {
        'success': True,
        'time_points': time_points,
        'hr_values': hr_values,
        'hr_values_smoothed': hr_values_smoothed,
        'sampling_rate': sampling_rate,
        'signal_length_seconds': len(ppg_signal) / sampling_rate,
        'estimation_parameters': {
            'peak_detection_method': peak_detection_method,
            'interpolation_rate': interpolation_rate,
            **kwargs
        }
    }
    
    # Step 4: Peak Information (if requested)
    if return_peaks:
        results['peaks'] = {
            'indices': peaks,
            'values': ppg_signal[peaks],
            'times': peaks / sampling_rate,
            'detection_info': peak_info
        }
    
    # Step 5: Statistical Analysis (if requested)
    if return_statistics:
        logger.info("Calculating heart rate statistics")
        
        # Basic statistics
        hr_stats = calculate_hr_statistics(hr_values_smoothed, time_points)
        results['statistics'] = hr_stats
    
    logger.info("Heart rate estimation completed successfully")
    
    return results



