"""
Denoising Module

Implements band-pass filtering for removing high-frequency noise and signal jitter
from PPG signals. The typical frequency range for PPG signals is 0.5-4 Hz.
"""

import numpy as np
from typing import Union, Tuple, Optional
from scipy import signal


def bandpass_filter(
    ppg_signal: np.ndarray,
    sampling_rate: float,
    low_cutoff: float = 0.5,
    high_cutoff: float = 4.0,
    filter_order: int = 4,
    filter_type: str = 'butterworth'
) -> np.ndarray:
    """
    Apply band-pass filter to PPG signal to remove noise and jitter.
    
    This function applies a band-pass filter to isolate the frequency range
    of interest for PPG signals (typically 0.5-4 Hz corresponding to 
    30-240 BPM heart rate range).
    
    Args:
        ppg_signal: Input PPG signal array
        sampling_rate: Sampling rate in Hz
        low_cutoff: Low cutoff frequency in Hz (default: 0.5 Hz)
        high_cutoff: High cutoff frequency in Hz (default: 4.0 Hz)
        filter_order: Filter order (default: 4)
        filter_type: Type of filter ('butterworth', 'chebyshev1', 'elliptic')
        
    Returns:
        Filtered PPG signal
        
    Raises:
        ValueError: If cutoff frequencies are invalid or signal is empty
        
    Example:
        >>> ppg_signal = np.array([...])  # Raw PPG data
        >>> filtered = bandpass_filter(ppg_signal, sampling_rate=64)
    """
    if len(ppg_signal) == 0:
        raise ValueError("Input signal cannot be empty")
    
    if not np.isfinite(ppg_signal).all():
        raise ValueError("Input signal contains non-finite values")
    
    if low_cutoff >= high_cutoff:
        raise ValueError("Low cutoff must be less than high cutoff")
    
    if high_cutoff >= sampling_rate / 2:
        raise ValueError("High cutoff must be less than Nyquist frequency")
    
    # Normalize cutoff frequencies
    nyquist = sampling_rate / 2
    low_norm = low_cutoff / nyquist
    high_norm = high_cutoff / nyquist
    
    # Design filter based on type
    if filter_type.lower() == 'butterworth':
        b, a = signal.butter(filter_order, [low_norm, high_norm], btype='band')
    elif filter_type.lower() == 'chebyshev1':
        b, a = signal.cheby1(filter_order, 1, [low_norm, high_norm], btype='band')
    elif filter_type.lower() == 'elliptic':
        b, a = signal.ellip(filter_order, 1, 40, [low_norm, high_norm], btype='band')
    else:
        raise ValueError(f"Unsupported filter type: {filter_type}")
    
    # Apply zero-phase filtering to avoid phase distortion
    filtered_signal = signal.filtfilt(b, a, ppg_signal)
    
    return filtered_signal



