"""
Detrending Module

Implements wavelet-based detrending for removing baseline drift from PPG signals.
Baseline drift is commonly caused by respiration and other low-frequency artifacts.
"""

import numpy as np
from typing import Union, Tuple, Optional
import pywt


def wavelet_detrend(
    signal: np.ndarray,
    wavelet: str = 'db4',
    levels: Optional[int] = None,
    mode: str = 'symmetric'
) -> np.ndarray:
    """
    Remove baseline drift from PPG signal using wavelet-based reconstruction.
    
    This function decomposes the signal using discrete wavelet transform,
    removes low-frequency components that represent baseline drift, and
    reconstructs the signal.
    
    Args:
        signal: Input PPG signal array
        wavelet: Wavelet type to use for decomposition (default: 'db4')
        levels: Number of decomposition levels (default: auto-calculated)
        mode: Signal extension mode for wavelet transform
        
    Returns:
        Detrended PPG signal
        
    Raises:
        ValueError: If signal is empty or contains invalid values
        
    Example:
        >>> ppg_signal = np.array([...])  # Raw PPG data
        >>> detrended = wavelet_detrend(ppg_signal)
    """
    if len(signal) == 0:
        raise ValueError("Input signal cannot be empty")
    
    if not np.isfinite(signal).all():
        raise ValueError("Input signal contains non-finite values")
    
    # Auto-calculate decomposition levels if not specified
    if levels is None:
        levels = min(6, int(np.log2(len(signal))))
    
    # Perform wavelet decomposition
    coeffs = pywt.wavedec(signal, wavelet, level=levels, mode=mode)
    
    # Remove low-frequency components (approximation coefficients)
    # Keep only detail coefficients to remove baseline drift
    coeffs[0] = np.zeros_like(coeffs[0])
    
    # Reconstruct signal without low-frequency components
    detrended_signal = pywt.waverec(coeffs, wavelet, mode=mode)
    
    # Ensure output length matches input length
    if len(detrended_signal) != len(signal):
        detrended_signal = detrended_signal[:len(signal)]
    
    return detrended_signal



