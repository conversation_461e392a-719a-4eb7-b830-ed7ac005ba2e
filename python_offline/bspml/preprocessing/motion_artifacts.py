"""
Motion Artifact Removal Module

Implements RLS (Recursive Least Squares) adaptive filtering using accelerometer
reference signals to remove motion-induced distortions from PPG signals.
"""

import numpy as np
from typing import Union, Tuple, Optional
from scipy import signal


class RLSFilter:
    """
    Recursive Least Squares adaptive filter for motion artifact removal.
    
    This filter uses accelerometer signals as reference to adaptively
    remove motion artifacts from PPG signals.
    """
    
    def __init__(
        self,
        filter_order: int = 8,
        forgetting_factor: float = 0.99,
        regularization: float = 1e-6
    ):
        """
        Initialize RLS filter.
        
        Args:
            filter_order: Order of the adaptive filter
            forgetting_factor: Forgetting factor (0 < lambda <= 1)
            regularization: Regularization parameter for numerical stability
        """
        self.filter_order = filter_order
        self.forgetting_factor = forgetting_factor
        self.regularization = regularization
        
        # Initialize filter coefficients and covariance matrix
        self.weights = np.zeros(filter_order)
        self.P = np.eye(filter_order) / regularization
        
    def reset(self):
        """Reset filter state."""
        self.weights = np.zeros(self.filter_order)
        self.P = np.eye(self.filter_order) / self.regularization
        
    def update(self, reference_vector: np.ndarray, desired_signal: float) -> float:
        """
        Update filter with one sample.
        
        Args:
            reference_vector: Reference signal vector (accelerometer data)
            desired_signal: Desired signal sample (PPG)
            
        Returns:
            Filtered output sample
        """
        if len(reference_vector) != self.filter_order:
            raise ValueError(f"Reference vector must have length {self.filter_order}")
        
        # Calculate filter output
        output = np.dot(self.weights, reference_vector)
        
        # Calculate error
        error = desired_signal - output
        
        # Update covariance matrix
        k = self.P @ reference_vector
        alpha = 1 / (self.forgetting_factor + reference_vector.T @ k)
        self.P = (self.P - alpha * np.outer(k, k)) / self.forgetting_factor
        
        # Update weights
        self.weights += alpha * error * k
        
        return error  # Return cleaned signal (error signal)


def rls_filter(
    ppg_signal: np.ndarray,
    acc_signals: np.ndarray,
    filter_order: int = 8,
    forgetting_factor: float = 0.99
) -> np.ndarray:
    """
    Apply RLS adaptive filtering to remove motion artifacts from PPG signal.
    
    This function uses accelerometer signals as reference to adaptively
    remove motion-induced artifacts from PPG signals.
    
    Args:
        ppg_signal: Input PPG signal array
        acc_signals: Accelerometer signals array (shape: [n_samples, n_axes])
                    Typically 3 axes (x, y, z) but can be more
        filter_order: Order of the adaptive filter
        forgetting_factor: Forgetting factor for RLS algorithm
        
    Returns:
        Motion artifact corrected PPG signal
        
    Raises:
        ValueError: If input dimensions don't match or are invalid
        
    Example:
        >>> ppg = np.array([...])  # PPG signal
        >>> acc = np.array([[ax1, ay1, az1], [ax2, ay2, az2], ...])  # ACC data
        >>> cleaned = rls_filter(ppg, acc)
    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")
    
    if acc_signals.ndim != 2:
        raise ValueError("Accelerometer signals must be 2D array")
    
    if len(ppg_signal) != acc_signals.shape[0]:
        raise ValueError("PPG and accelerometer signals must have same length")
    
    n_samples, n_axes = acc_signals.shape
    
    # Create reference signal matrix by combining accelerometer axes
    # and their delayed versions to capture motion dynamics
    reference_matrix = create_reference_matrix(acc_signals, filter_order)
    
    # Initialize RLS filter
    rls = RLSFilter(filter_order, forgetting_factor)
    
    # Process signal sample by sample
    cleaned_signal = np.zeros_like(ppg_signal)
    
    for i in range(filter_order, n_samples):
        ref_vector = reference_matrix[i, :]
        cleaned_signal[i] = rls.update(ref_vector, ppg_signal[i])
    
    # Copy initial samples (before filter has enough history)
    cleaned_signal[:filter_order] = ppg_signal[:filter_order]
    
    return cleaned_signal


def create_reference_matrix(
    acc_signals: np.ndarray,
    filter_order: int
) -> np.ndarray:
    """
    Create reference signal matrix from accelerometer data.
    
    This function creates a matrix of reference signals by combining
    accelerometer axes and their time-delayed versions.
    
    Args:
        acc_signals: Accelerometer signals [n_samples, n_axes]
        filter_order: Filter order (number of taps)
        
    Returns:
        Reference matrix [n_samples, filter_order]
    """
    n_samples, n_axes = acc_signals.shape
    
    # Create time-delayed versions of accelerometer signals
    reference_matrix = np.zeros((n_samples, filter_order))
    
    # Fill reference matrix with delayed versions of accelerometer signals
    taps_per_axis = filter_order // n_axes
    remaining_taps = filter_order % n_axes
    
    col_idx = 0
    for axis in range(n_axes):
        # Number of taps for this axis
        n_taps = taps_per_axis + (1 if axis < remaining_taps else 0)
        
        for delay in range(n_taps):
            if col_idx < filter_order:
                if delay < n_samples:
                    reference_matrix[delay:, col_idx] = acc_signals[:-delay if delay > 0 else None, axis]
                col_idx += 1
    
    return reference_matrix


def nlms_filter(
    ppg_signal: np.ndarray,
    acc_signals: np.ndarray,
    filter_order: int = 8,
    step_size: float = 0.01,
    regularization: float = 1e-6
) -> np.ndarray:
    """
    Apply Normalized Least Mean Squares (NLMS) adaptive filtering.
    
    Alternative to RLS with lower computational complexity.
    
    Args:
        ppg_signal: Input PPG signal array
        acc_signals: Accelerometer signals array
        filter_order: Order of the adaptive filter
        step_size: NLMS step size parameter
        regularization: Regularization parameter
        
    Returns:
        Motion artifact corrected PPG signal
    """
    if len(ppg_signal) != acc_signals.shape[0]:
        raise ValueError("PPG and accelerometer signals must have same length")
    
    n_samples = len(ppg_signal)
    reference_matrix = create_reference_matrix(acc_signals, filter_order)
    
    # Initialize filter weights
    weights = np.zeros(filter_order)
    cleaned_signal = np.zeros_like(ppg_signal)
    
    for i in range(filter_order, n_samples):
        ref_vector = reference_matrix[i, :]
        
        # Calculate filter output
        output = np.dot(weights, ref_vector)
        
        # Calculate error (cleaned signal)
        error = ppg_signal[i] - output
        cleaned_signal[i] = error
        
        # Update weights using NLMS
        norm_factor = np.dot(ref_vector, ref_vector) + regularization
        weights += (step_size / norm_factor) * error * ref_vector
    
    # Copy initial samples
    cleaned_signal[:filter_order] = ppg_signal[:filter_order]
    
    return cleaned_signal


def validate_motion_artifact_removal(
    original_ppg: np.ndarray,
    cleaned_ppg: np.ndarray,
    acc_signals: np.ndarray,
    sampling_rate: float
) -> dict:
    """
    Validate the quality of motion artifact removal.
    
    Args:
        original_ppg: Original PPG signal
        cleaned_ppg: Cleaned PPG signal
        acc_signals: Accelerometer signals
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Dictionary containing quality metrics
    """
    # Calculate correlation with accelerometer signals
    acc_corr_orig = np.max([
        np.abs(np.corrcoef(original_ppg, acc_signals[:, i])[0, 1])
        for i in range(acc_signals.shape[1])
    ])
    
    acc_corr_cleaned = np.max([
        np.abs(np.corrcoef(cleaned_ppg, acc_signals[:, i])[0, 1])
        for i in range(acc_signals.shape[1])
    ])
    
    # Calculate signal quality metrics
    snr_orig = calculate_snr(original_ppg, sampling_rate)
    snr_cleaned = calculate_snr(cleaned_ppg, sampling_rate)
    
    return {
        'motion_correlation_reduction': (acc_corr_orig - acc_corr_cleaned) / acc_corr_orig * 100,
        'snr_improvement_db': snr_cleaned - snr_orig,
        'signal_preservation_ratio': np.corrcoef(original_ppg, cleaned_ppg)[0, 1],
        'rms_reduction_percent': (1 - np.sqrt(np.mean(cleaned_ppg**2)) / np.sqrt(np.mean(original_ppg**2))) * 100
    }


def calculate_snr(ppg_signal: np.ndarray, sampling_rate: float) -> float:
    """Calculate signal-to-noise ratio of PPG signal."""
    # Calculate power in PPG frequency band (0.5-4 Hz)
    freqs, psd = signal.welch(ppg_signal, sampling_rate)
    signal_mask = (freqs >= 0.5) & (freqs <= 4.0)
    noise_mask = ~signal_mask
    
    signal_power = np.sum(psd[signal_mask])
    noise_power = np.sum(psd[noise_mask])
    
    if noise_power > 0:
        return 10 * np.log10(signal_power / noise_power)
    else:
        return float('inf')
