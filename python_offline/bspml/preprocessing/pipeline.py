"""
Preprocessing Pipeline Module

Combines all preprocessing steps into a unified pipeline for PPG signal processing.
"""

import numpy as np
from typing import Optional, Dict, Any, Tuple
import logging

from .detrending import wavelet_detrend, adaptive_wavelet_detrend
from .denoising import bandpass_filter, adaptive_bandpass_filter, notch_filter
from .motion_artifacts import rls_filter, nlms_filter


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def preprocess_ppg(
    ppg_signal: np.ndarray,
    acc_signals: Optional[np.ndarray] = None,
    sampling_rate: float = 64.0,
    enable_detrending: bool = True,
    enable_denoising: bool = True,
    enable_motion_removal: bool = True,
    detrending_method: str = 'wavelet',
    denoising_method: str = 'bandpass',
    motion_removal_method: str = 'rls',
    preprocessing_params: Optional[Dict[str, Any]] = None
) -> np.ndarray:
    """
    Complete PPG signal preprocessing pipeline.
    
    This function applies the complete preprocessing pipeline including:
    1. Detrending (baseline drift removal)
    2. Denoising (high-frequency noise removal)
    3. Motion artifact removal (if accelerometer data available)
    
    Args:
        ppg_signal: Input PPG signal array
        acc_signals: Accelerometer signals [n_samples, n_axes] (optional)
        sampling_rate: Sampling rate in Hz
        enable_detrending: Whether to apply detrending
        enable_denoising: Whether to apply denoising
        enable_motion_removal: Whether to apply motion artifact removal
        detrending_method: Method for detrending ('wavelet', 'adaptive_wavelet')
        denoising_method: Method for denoising ('bandpass', 'adaptive_bandpass')
        motion_removal_method: Method for motion removal ('rls', 'nlms')
        preprocessing_params: Additional parameters for preprocessing methods
        
    Returns:
        Preprocessed PPG signal
        
    Raises:
        ValueError: If input parameters are invalid
        
    Example:
        >>> ppg = np.array([...])  # Raw PPG signal
        >>> acc = np.array([[ax, ay, az], ...])  # Accelerometer data
        >>> cleaned = preprocess_ppg(ppg, acc, sampling_rate=64)
    """
    if len(ppg_signal) == 0:
        raise ValueError("PPG signal cannot be empty")
    
    if not np.isfinite(ppg_signal).all():
        raise ValueError("PPG signal contains non-finite values")
    
    # Initialize parameters
    if preprocessing_params is None:
        preprocessing_params = {}
    
    # Start with original signal
    processed_signal = ppg_signal.copy()
    
    logger.info(f"Starting PPG preprocessing pipeline for {len(ppg_signal)} samples")
    
    # Step 1: Detrending (baseline drift removal)
    if enable_detrending:
        logger.info(f"Applying detrending using {detrending_method} method")
        
        if detrending_method == 'wavelet':
            wavelet_params = preprocessing_params.get('wavelet', {})
            processed_signal = wavelet_detrend(processed_signal, **wavelet_params)
            
        elif detrending_method == 'adaptive_wavelet':
            adaptive_params = preprocessing_params.get('adaptive_wavelet', {})
            processed_signal = adaptive_wavelet_detrend(
                processed_signal, 
                sampling_rate, 
                **adaptive_params
            )
        else:
            raise ValueError(f"Unknown detrending method: {detrending_method}")
    
    # Step 2: Denoising (high-frequency noise removal)
    if enable_denoising:
        logger.info(f"Applying denoising using {denoising_method} method")
        
        if denoising_method == 'bandpass':
            bandpass_params = preprocessing_params.get('bandpass', {})
            processed_signal = bandpass_filter(
                processed_signal, 
                sampling_rate, 
                **bandpass_params
            )
            
        elif denoising_method == 'adaptive_bandpass':
            adaptive_params = preprocessing_params.get('adaptive_bandpass', {})
            processed_signal = adaptive_bandpass_filter(
                processed_signal, 
                sampling_rate, 
                **adaptive_params
            )
        else:
            raise ValueError(f"Unknown denoising method: {denoising_method}")
        
        # Apply notch filter if specified
        if 'notch_freq' in preprocessing_params:
            notch_params = preprocessing_params.get('notch', {})
            processed_signal = notch_filter(
                processed_signal, 
                sampling_rate, 
                preprocessing_params['notch_freq'],
                **notch_params
            )
    
    # Step 3: Motion artifact removal
    if enable_motion_removal and acc_signals is not None:
        logger.info(f"Applying motion artifact removal using {motion_removal_method} method")
        
        if motion_removal_method == 'rls':
            rls_params = preprocessing_params.get('rls', {})
            processed_signal = rls_filter(
                processed_signal, 
                acc_signals, 
                **rls_params
            )
            
        elif motion_removal_method == 'nlms':
            nlms_params = preprocessing_params.get('nlms', {})
            processed_signal = nlms_filter(
                processed_signal, 
                acc_signals, 
                **nlms_params
            )
        else:
            raise ValueError(f"Unknown motion removal method: {motion_removal_method}")
    
    elif enable_motion_removal and acc_signals is None:
        logger.warning("Motion artifact removal requested but no accelerometer data provided")
    
    logger.info("PPG preprocessing pipeline completed")
    
    return processed_signal


def preprocess_ppg_with_quality_assessment(
    ppg_signal: np.ndarray,
    acc_signals: Optional[np.ndarray] = None,
    sampling_rate: float = 64.0,
    **kwargs
) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Preprocess PPG signal and return quality assessment metrics.
    
    Args:
        ppg_signal: Input PPG signal
        acc_signals: Accelerometer signals (optional)
        sampling_rate: Sampling rate in Hz
        **kwargs: Additional preprocessing parameters
        
    Returns:
        Tuple of (preprocessed_signal, quality_metrics)
    """
    # Store original signal for comparison
    original_signal = ppg_signal.copy()
    
    # Apply preprocessing
    processed_signal = preprocess_ppg(
        ppg_signal, 
        acc_signals, 
        sampling_rate, 
        **kwargs
    )
    
    # Calculate quality metrics
    quality_metrics = assess_preprocessing_quality(
        original_signal, 
        processed_signal, 
        acc_signals, 
        sampling_rate
    )
    
    return processed_signal, quality_metrics


def assess_preprocessing_quality(
    original: np.ndarray,
    processed: np.ndarray,
    acc_signals: Optional[np.ndarray],
    sampling_rate: float
) -> Dict[str, Any]:
    """
    Assess the quality of preprocessing operations.
    
    Args:
        original: Original PPG signal
        processed: Processed PPG signal
        acc_signals: Accelerometer signals (optional)
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Dictionary containing quality assessment metrics
    """
    from scipy import signal
    
    # Basic signal statistics
    metrics = {
        'signal_length': len(processed),
        'sampling_rate': sampling_rate,
        'rms_original': np.sqrt(np.mean(original**2)),
        'rms_processed': np.sqrt(np.mean(processed**2)),
        'signal_correlation': np.corrcoef(original, processed)[0, 1]
    }
    
    # Frequency domain analysis
    freqs, psd_orig = signal.welch(original, sampling_rate)
    freqs, psd_proc = signal.welch(processed, sampling_rate)
    
    # PPG frequency band power (0.5-4 Hz)
    ppg_band_mask = (freqs >= 0.5) & (freqs <= 4.0)
    ppg_power_orig = np.sum(psd_orig[ppg_band_mask])
    ppg_power_proc = np.sum(psd_proc[ppg_band_mask])
    
    metrics['ppg_band_power_ratio'] = ppg_power_proc / ppg_power_orig if ppg_power_orig > 0 else 0
    
    # Noise reduction (power outside PPG band)
    noise_mask = ~ppg_band_mask
    noise_power_orig = np.sum(psd_orig[noise_mask])
    noise_power_proc = np.sum(psd_proc[noise_mask])
    
    metrics['noise_reduction_percent'] = (
        (noise_power_orig - noise_power_proc) / noise_power_orig * 100 
        if noise_power_orig > 0 else 0
    )
    
    # SNR improvement
    snr_orig = 10 * np.log10(ppg_power_orig / noise_power_orig) if noise_power_orig > 0 else float('inf')
    snr_proc = 10 * np.log10(ppg_power_proc / noise_power_proc) if noise_power_proc > 0 else float('inf')
    
    metrics['snr_original_db'] = snr_orig
    metrics['snr_processed_db'] = snr_proc
    metrics['snr_improvement_db'] = snr_proc - snr_orig
    
    # Motion correlation (if accelerometer data available)
    if acc_signals is not None:
        motion_corr_orig = np.max([
            np.abs(np.corrcoef(original, acc_signals[:, i])[0, 1])
            for i in range(acc_signals.shape[1])
        ])
        motion_corr_proc = np.max([
            np.abs(np.corrcoef(processed, acc_signals[:, i])[0, 1])
            for i in range(acc_signals.shape[1])
        ])
        
        metrics['motion_correlation_original'] = motion_corr_orig
        metrics['motion_correlation_processed'] = motion_corr_proc
        metrics['motion_correlation_reduction_percent'] = (
            (motion_corr_orig - motion_corr_proc) / motion_corr_orig * 100
            if motion_corr_orig > 0 else 0
        )
    
    return metrics


def get_default_preprocessing_params() -> Dict[str, Any]:
    """
    Get default preprocessing parameters.
    
    Returns:
        Dictionary with default parameters for all preprocessing methods
    """
    return {
        'wavelet': {
            'wavelet': 'db4',
            'levels': None,
            'mode': 'symmetric'
        },
        'adaptive_wavelet': {
            'cutoff_freq': 0.1,
            'wavelet': 'db4'
        },
        'bandpass': {
            'low_cutoff': 0.5,
            'high_cutoff': 4.0,
            'filter_order': 4,
            'filter_type': 'butterworth'
        },
        'adaptive_bandpass': {
            'estimated_hr': None,
            'hr_range': (30, 240)
        },
        'notch': {
            'quality_factor': 30.0
        },
        'rls': {
            'filter_order': 8,
            'forgetting_factor': 0.99
        },
        'nlms': {
            'filter_order': 8,
            'step_size': 0.01,
            'regularization': 1e-6
        }
    }
