[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "bspml"
dynamic = ["version"]
description = "Biosignal Processing and Machine Learning for PPG Heart Rate Estimation"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "BSPML Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "BSPML Team", email = "<EMAIL>"}
]
keywords = [
    "biosignal processing",
    "ppg",
    "photoplethysmography",
    "heart rate",
    "signal processing",
    "machine learning",
    "biomedical engineering"
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Healthcare Industry",
    "Topic :: Scientific/Engineering :: Bio-Informatics",
    "Topic :: Scientific/Engineering :: Medical Science Apps.",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Operating System :: OS Independent"
]
requires-python = ">=3.7"
dependencies = [
    "numpy>=1.19.0",
    "scipy>=1.7.0",
    "pywt>=1.1.1",
    "matplotlib>=3.3.0",
    "pandas>=1.3.0",
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0"
]

[project.urls]
Homepage = "https://github.com/bspml/bspml_project"
Documentation = "https://bspml.readthedocs.io/"
Repository = "https://github.com/bspml/bspml_project.git"
"Bug Tracker" = "https://github.com/bspml/bspml_project/issues"



[tool.setuptools]
packages = ["bspml"]
package-dir = {"" = "."}

[tool.setuptools.package-data]
bspml = [
    "data/sample_data/*.csv",
    "data/sample_data/*.h5",
    "config/*.yaml",
    "config/*.json"
]

[tool.setuptools_scm]
write_to = "bspml/_version.py"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["bspml"]
known_third_party = ["numpy", "scipy", "matplotlib", "pandas", "sklearn", "pywt"]

# MyPy configuration
[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "scipy.*",
    "matplotlib.*",
    "pandas.*",
    "sklearn.*",
    "pywt.*",
    "h5py.*",
    "tqdm.*"
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--strict-markers",
    "--cov=bspml",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-fail-under=80"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning"
]

# Coverage configuration
[tool.coverage.run]
source = ["bspml"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/cli/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
