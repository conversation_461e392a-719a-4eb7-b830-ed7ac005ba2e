# Include base requirements
-r requirements.txt

# Testing
pytest>=6.0.0
pytest-cov>=2.12.0
pytest-mock>=3.6.0
pytest-xdist>=2.4.0
pytest-benchmark>=3.4.0

# Code quality
black>=21.0.0
flake8>=3.9.0
isort>=5.9.0
mypy>=0.910
pre-commit>=2.15.0

# Documentation
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0
sphinx-autodoc-typehints>=1.12.0
myst-parser>=0.15.0
pdoc3>=0.10.0

# Jupyter and interactive development
jupyter>=1.0.0
ipykernel>=6.0.0
ipywidgets>=7.6.0
jupyterlab>=3.0.0

# Advanced visualization
plotly>=5.0.0
seaborn>=0.11.0

# Profiling and performance
line_profiler>=3.3.0
memory_profiler>=0.60.0

# Build tools
build>=0.7.0
twine>=3.4.0
wheel>=0.37.0
