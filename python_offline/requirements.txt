# Core scientific computing
numpy>=1.19.0
scipy>=1.7.0

# Signal processing
pywt>=1.1.1

# Data handling and visualization
matplotlib>=3.3.0
pandas>=1.3.0
h5py>=3.1.0
tqdm>=4.60.0

# Machine learning (optional, for advanced features)
scikit-learn>=1.0.0

# Configuration and utilities
pyyaml>=5.4.0
click>=8.0.0

# Optional: Jupyter support
# jupyter>=1.0.0
# ipykernel>=6.0.0

# Optional: Advanced visualization
# plotly>=5.0.0
# seaborn>=0.11.0
