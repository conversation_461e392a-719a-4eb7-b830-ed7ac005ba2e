"""
Setup script for BSPML (Biosignal Processing and Machine Learning) package.

This package provides comprehensive PPG signal preprocessing and heart rate
estimation algorithms for both offline analysis and real-time applications.
"""

from setuptools import setup, find_packages
import os
import sys

# Read version from __init__.py
def get_version():
    version_file = os.path.join(os.path.dirname(__file__), 'bspml', '__init__.py')
    with open(version_file, 'r') as f:
        for line in f:
            if line.startswith('__version__'):
                return line.split('=')[1].strip().strip('"').strip("'")
    return '0.1.0'

# Read long description from README
def get_long_description():
    readme_path = os.path.join(os.path.dirname(__file__), '..', 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Biosignal Processing and Machine Learning package for PPG signal analysis"

# Read requirements
def get_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return [
        'numpy>=1.19.0',
        'scipy>=1.7.0',
        'pywt>=1.1.1',
        'matplotlib>=3.3.0',
        'pandas>=1.3.0'
    ]

# Check Python version
if sys.version_info < (3, 7):
    sys.exit('Python 3.7 or higher is required')

setup(
    name='bspml',
    version=get_version(),
    author='BSPML Team',
    author_email='<EMAIL>',
    description='Biosignal Processing and Machine Learning for PPG Heart Rate Estimation',
    long_description=get_long_description(),
    long_description_content_type='text/markdown',
    url='https://github.com/bspml/bspml_project',
    project_urls={
        'Bug Reports': 'https://github.com/bspml/bspml_project/issues',
        'Source': 'https://github.com/bspml/bspml_project',
        'Documentation': 'https://bspml.readthedocs.io/',
    },
    
    # Package configuration
    packages=find_packages(exclude=['tests', 'tests.*']),
    package_data={
        'bspml': [
            'data/sample_data/*.csv',
            'data/sample_data/*.h5',
            'config/*.yaml',
            'config/*.json'
        ]
    },
    include_package_data=True,
    
    # Dependencies
    python_requires='>=3.7',
    install_requires=get_requirements(),
    
    # Classification
    classifiers=[
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Science/Research',
        'Intended Audience :: Healthcare Industry',
        'Topic :: Scientific/Engineering :: Bio-Informatics',
        'Topic :: Scientific/Engineering :: Medical Science Apps.',
        'Topic :: Software Development :: Libraries :: Python Modules',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Operating System :: OS Independent',
        'Natural Language :: English'
    ],
    
    # Keywords
    keywords=[
        'biosignal processing',
        'ppg',
        'photoplethysmography', 
        'heart rate',
        'signal processing',
        'machine learning',
        'biomedical engineering',
        'wearable devices',
        'health monitoring'
    ],
    
    # Additional metadata
    zip_safe=False
)
