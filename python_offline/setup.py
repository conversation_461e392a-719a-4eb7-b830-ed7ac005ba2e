"""
Setup script for BSPML (Biosignal Processing and Machine Learning) package.

This package provides comprehensive PPG signal preprocessing and heart rate
estimation algorithms for both offline analysis and real-time applications.
"""

from setuptools import setup, find_packages
import os
import sys

# Read version from __init__.py
def get_version():
    version_file = os.path.join(os.path.dirname(__file__), 'bspml', '__init__.py')
    with open(version_file, 'r') as f:
        for line in f:
            if line.startswith('__version__'):
                return line.split('=')[1].strip().strip('"').strip("'")
    return '0.1.0'

# Read long description from README
def get_long_description():
    readme_path = os.path.join(os.path.dirname(__file__), '..', 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "Biosignal Processing and Machine Learning package for PPG signal analysis"

# Read requirements
def get_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return [
        'numpy>=1.19.0',
        'scipy>=1.7.0',
        'pywt>=1.1.1',
        'matplotlib>=3.3.0',
        'pandas>=1.3.0',
        'scikit-learn>=1.0.0',
        'h5py>=3.1.0',
        'tqdm>=4.60.0'
    ]

def get_dev_requirements():
    return [
        'pytest>=6.0.0',
        'pytest-cov>=2.12.0',
        'pytest-mock>=3.6.0',
        'black>=21.0.0',
        'flake8>=3.9.0',
        'mypy>=0.910',
        'sphinx>=4.0.0',
        'sphinx-rtd-theme>=0.5.0',
        'pdoc3>=0.10.0',
        'jupyter>=1.0.0',
        'ipykernel>=6.0.0'
    ]

# Check Python version
if sys.version_info < (3, 7):
    sys.exit('Python 3.7 or higher is required')

setup(
    name='bspml',
    version=get_version(),
    author='BSPML Team',
    author_email='<EMAIL>',
    description='Biosignal Processing and Machine Learning for PPG Heart Rate Estimation',
    long_description=get_long_description(),
    long_description_content_type='text/markdown',
    url='https://github.com/bspml/bspml_project',
    project_urls={
        'Bug Reports': 'https://github.com/bspml/bspml_project/issues',
        'Source': 'https://github.com/bspml/bspml_project',
        'Documentation': 'https://bspml.readthedocs.io/',
    },
    
    # Package configuration
    packages=find_packages(exclude=['tests', 'tests.*']),
    package_data={
        'bspml': [
            'data/sample_data/*.csv',
            'data/sample_data/*.h5',
            'config/*.yaml',
            'config/*.json'
        ]
    },
    include_package_data=True,
    
    # Dependencies
    python_requires='>=3.7',
    install_requires=get_requirements(),
    extras_require={
        'dev': get_dev_requirements(),
        'docs': [
            'sphinx>=4.0.0',
            'sphinx-rtd-theme>=0.5.0',
            'myst-parser>=0.15.0',
            'sphinx-autodoc-typehints>=1.12.0'
        ],
        'jupyter': [
            'jupyter>=1.0.0',
            'ipykernel>=6.0.0',
            'ipywidgets>=7.6.0',
            'plotly>=5.0.0'
        ],
        'complete': get_dev_requirements() + [
            'sphinx>=4.0.0',
            'sphinx-rtd-theme>=0.5.0',
            'jupyter>=1.0.0',
            'ipykernel>=6.0.0',
            'plotly>=5.0.0'
        ]
    },
    
    # Entry points
    entry_points={
        'console_scripts': [
            'bspml-process=bspml.cli.process:main',
            'bspml-evaluate=bspml.cli.evaluate:main',
            'bspml-benchmark=bspml.cli.benchmark:main',
            'bspml-convert=bspml.cli.convert:main'
        ]
    },
    
    # Classification
    classifiers=[
        'Development Status :: 3 - Alpha',
        'Intended Audience :: Science/Research',
        'Intended Audience :: Healthcare Industry',
        'Topic :: Scientific/Engineering :: Bio-Informatics',
        'Topic :: Scientific/Engineering :: Medical Science Apps.',
        'Topic :: Software Development :: Libraries :: Python Modules',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Operating System :: OS Independent',
        'Natural Language :: English'
    ],
    
    # Keywords
    keywords=[
        'biosignal processing',
        'ppg',
        'photoplethysmography', 
        'heart rate',
        'signal processing',
        'machine learning',
        'biomedical engineering',
        'wearable devices',
        'health monitoring'
    ],
    
    # Additional metadata
    zip_safe=False,
    test_suite='tests',
    tests_require=[
        'pytest>=6.0.0',
        'pytest-cov>=2.12.0'
    ]
)
