"""
Pytest configuration and fixtures for BSPML tests.

This module provides common fixtures and test data for all test modules.
"""

import pytest
import numpy as np
from typing import Tu<PERSON>, Dict, Any


@pytest.fixture
def sample_ppg_signal() -> np.ndarray:
    """Generate a synthetic PPG signal for testing."""
    # Create a synthetic PPG signal with known characteristics
    duration = 30  # seconds
    sampling_rate = 64  # Hz
    heart_rate = 75  # BPM
    
    t = np.linspace(0, duration, int(duration * sampling_rate))
    
    # Base PPG signal (simplified model)
    hr_freq = heart_rate / 60  # Hz
    ppg = np.sin(2 * np.pi * hr_freq * t) + 0.3 * np.sin(4 * np.pi * hr_freq * t)
    
    # Add baseline drift (respiration-like)
    baseline = 0.1 * np.sin(2 * np.pi * 0.3 * t)
    
    # Add noise
    noise = 0.05 * np.random.randn(len(t))
    
    return ppg + baseline + noise


@pytest.fixture
def sample_acc_signals() -> np.ndarray:
    """Generate synthetic accelerometer signals for testing."""
    duration = 30  # seconds
    sampling_rate = 64  # Hz
    
    t = np.linspace(0, duration, int(duration * sampling_rate))
    
    # Simulate motion artifacts
    motion_freq = 2.0  # Hz
    acc_x = 0.2 * np.sin(2 * np.pi * motion_freq * t) + 0.1 * np.random.randn(len(t))
    acc_y = 0.15 * np.cos(2 * np.pi * motion_freq * t) + 0.1 * np.random.randn(len(t))
    acc_z = 0.1 * np.sin(2 * np.pi * motion_freq * t + np.pi/4) + 0.1 * np.random.randn(len(t))
    
    return np.column_stack([acc_x, acc_y, acc_z])


@pytest.fixture
def sampling_rate() -> float:
    """Standard sampling rate for tests."""
    return 64.0


@pytest.fixture
def ppg_with_artifacts() -> np.ndarray:
    """Generate PPG signal with various artifacts for testing preprocessing."""
    duration = 60  # seconds
    sampling_rate = 64  # Hz
    
    t = np.linspace(0, duration, int(duration * sampling_rate))
    
    # Clean PPG signal
    hr_freq = 70 / 60  # 70 BPM
    clean_ppg = np.sin(2 * np.pi * hr_freq * t) + 0.3 * np.sin(4 * np.pi * hr_freq * t)
    
    # Add artifacts
    # 1. Baseline drift (low frequency)
    baseline_drift = 0.3 * np.sin(2 * np.pi * 0.05 * t) + 0.2 * np.sin(2 * np.pi * 0.1 * t)
    
    # 2. High frequency noise
    hf_noise = 0.1 * np.random.randn(len(t))
    
    # 3. Motion artifacts (correlated with accelerometer)
    motion_artifact = 0.4 * np.sin(2 * np.pi * 1.5 * t) * (t > 20) * (t < 40)  # Motion during middle section
    
    # 4. Power line interference (50 Hz)
    power_line = 0.05 * np.sin(2 * np.pi * 50 * t)
    
    return clean_ppg + baseline_drift + hf_noise + motion_artifact + power_line


@pytest.fixture
def known_peaks() -> np.ndarray:
    """Generate known peak locations for testing peak detection."""
    # For a 75 BPM signal sampled at 64 Hz for 30 seconds
    # Inter-beat interval = 60/75 = 0.8 seconds = 51.2 samples
    sampling_rate = 64
    duration = 30
    hr_bpm = 75
    
    ibi_samples = sampling_rate * 60 / hr_bpm
    num_peaks = int(duration * hr_bpm / 60)
    
    # Generate peak locations with slight variability
    peaks = []
    current_pos = ibi_samples
    
    for i in range(num_peaks):
        if current_pos < duration * sampling_rate:
            # Add small random variation (±5% of IBI)
            variation = np.random.uniform(-0.05, 0.05) * ibi_samples
            peaks.append(int(current_pos + variation))
            current_pos += ibi_samples
    
    return np.array(peaks)


@pytest.fixture
def test_parameters() -> Dict[str, Any]:
    """Common test parameters."""
    return {
        'sampling_rate': 64.0,
        'duration': 30.0,
        'heart_rate_bpm': 75.0,
        'noise_level': 0.05,
        'motion_frequency': 2.0
    }


@pytest.fixture
def preprocessing_params() -> Dict[str, Any]:
    """Default preprocessing parameters for tests."""
    return {
        'wavelet': {
            'wavelet': 'db4',
            'levels': 6,
            'mode': 'symmetric'
        },
        'bandpass': {
            'low_cutoff': 0.5,
            'high_cutoff': 4.0,
            'filter_order': 4,
            'filter_type': 'butterworth'
        },
        'rls': {
            'filter_order': 8,
            'forgetting_factor': 0.99
        }
    }


@pytest.fixture
def hr_estimation_params() -> Dict[str, Any]:
    """Default heart rate estimation parameters for tests."""
    return {
        'window_size': 10.0,
        'overlap': 0.5,
        'min_peak_distance': 0.3,
        'adaptive_threshold': True
    }


# Utility functions for tests
def assert_signal_properties(signal: np.ndarray, expected_length: int, tolerance: float = 0.1):
    """Assert basic signal properties."""
    assert len(signal) == expected_length, f"Expected length {expected_length}, got {len(signal)}"
    assert np.isfinite(signal).all(), "Signal contains non-finite values"
    assert not np.isnan(signal).any(), "Signal contains NaN values"


def assert_heart_rate_range(hr_values: np.ndarray, min_hr: float = 30, max_hr: float = 200):
    """Assert heart rate values are in physiological range."""
    assert np.all(hr_values >= min_hr), f"Heart rate values below {min_hr} BPM found"
    assert np.all(hr_values <= max_hr), f"Heart rate values above {max_hr} BPM found"


def calculate_signal_snr(signal: np.ndarray, noise: np.ndarray) -> float:
    """Calculate signal-to-noise ratio."""
    signal_power = np.mean(signal**2)
    noise_power = np.mean(noise**2)
    
    if noise_power > 0:
        return 10 * np.log10(signal_power / noise_power)
    else:
        return float('inf')


# Test data generators
def generate_test_dataset(
    num_subjects: int = 5,
    duration_per_subject: float = 60.0,
    sampling_rate: float = 64.0
) -> Dict[str, Any]:
    """Generate a synthetic test dataset."""
    dataset = {
        'subjects': [],
        'metadata': {
            'num_subjects': num_subjects,
            'duration_per_subject': duration_per_subject,
            'sampling_rate': sampling_rate
        }
    }
    
    for subject_id in range(num_subjects):
        # Vary heart rate between subjects
        base_hr = 60 + subject_id * 10  # 60, 70, 80, 90, 100 BPM
        
        # Generate signals
        t = np.linspace(0, duration_per_subject, int(duration_per_subject * sampling_rate))
        hr_freq = base_hr / 60
        
        ppg = np.sin(2 * np.pi * hr_freq * t) + 0.3 * np.sin(4 * np.pi * hr_freq * t)
        ppg += 0.05 * np.random.randn(len(t))  # Add noise
        
        # Generate accelerometer data
        acc_x = 0.1 * np.random.randn(len(t))
        acc_y = 0.1 * np.random.randn(len(t))
        acc_z = 0.1 * np.random.randn(len(t))
        acc = np.column_stack([acc_x, acc_y, acc_z])
        
        dataset['subjects'].append({
            'subject_id': subject_id,
            'ppg_signal': ppg,
            'acc_signals': acc,
            'true_heart_rate': base_hr,
            'time_vector': t
        })
    
    return dataset
