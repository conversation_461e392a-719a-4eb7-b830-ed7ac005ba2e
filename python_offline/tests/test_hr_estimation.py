"""
Tests for heart rate estimation module.

This module contains comprehensive tests for peak detection and
heart rate calculation functions.
"""

import pytest
import numpy as np

from bspml.hr_estimation import (
    find_peaks_sliding_window,
    detect_ppg_peaks,
    calculate_instantaneous_hr,
    calculate_hr_statistics,
    estimate_heart_rate
)
from bspml.hr_estimation.peak_detection import (
    adaptive_peak_detection,
    template_matching_peaks,
    validate_peak_detection
)
from bspml.hr_estimation.hr_calculation import (
    smooth_hr_signal,
    detect_hr_anomalies,
    calculate_hrv_frequency_domain
)
from bspml.hr_estimation.pipeline import (
    estimate_heart_rate_realtime,
    assess_hr_quality
)


class TestPeakDetection:
    """Test cases for peak detection functions."""
    
    def test_find_peaks_sliding_window_basic(self, sample_ppg_signal, sampling_rate):
        """Test basic sliding window peak detection."""
        peaks, values = find_peaks_sliding_window(sample_ppg_signal, sampling_rate)
        
        assert len(peaks) > 0, "Should detect at least some peaks"
        assert len(peaks) == len(values), "Peaks and values should have same length"
        assert np.all(peaks >= 0), "Peak indices should be non-negative"
        assert np.all(peaks < len(sample_ppg_signal)), "Peak indices should be within signal bounds"
        
        # Check that peaks are actual local maxima
        for peak_idx in peaks:
            if peak_idx > 0 and peak_idx < len(sample_ppg_signal) - 1:
                assert sample_ppg_signal[peak_idx] >= sample_ppg_signal[peak_idx - 1]
                assert sample_ppg_signal[peak_idx] >= sample_ppg_signal[peak_idx + 1]
    
    def test_find_peaks_sliding_window_empty_signal(self, sampling_rate):
        """Test peak detection with empty signal."""
        with pytest.raises(ValueError, match="PPG signal cannot be empty"):
            find_peaks_sliding_window(np.array([]), sampling_rate)
    
    def test_find_peaks_sliding_window_invalid_sampling_rate(self, sample_ppg_signal):
        """Test peak detection with invalid sampling rate."""
        with pytest.raises(ValueError, match="Sampling rate must be positive"):
            find_peaks_sliding_window(sample_ppg_signal, -1.0)
    
    @pytest.mark.parametrize("window_size,overlap", [
        (5.0, 0.25),
        (10.0, 0.5),
        (15.0, 0.75)
    ])
    def test_find_peaks_sliding_window_parameters(self, sample_ppg_signal, sampling_rate, window_size, overlap):
        """Test peak detection with different window parameters."""
        peaks, values = find_peaks_sliding_window(
            sample_ppg_signal, 
            sampling_rate,
            window_size=window_size,
            overlap=overlap
        )
        
        assert len(peaks) >= 0  # May be zero for very short signals
        assert len(peaks) == len(values)
    
    def test_detect_ppg_peaks_methods(self, sample_ppg_signal, sampling_rate):
        """Test different peak detection methods."""
        methods = ['sliding_window', 'adaptive', 'template']
        
        for method in methods:
            peaks, info = detect_ppg_peaks(sample_ppg_signal, sampling_rate, method=method)
            
            assert isinstance(peaks, np.ndarray)
            assert isinstance(info, dict)
            assert info['method'] == method
            assert 'num_peaks' in info
    
    def test_adaptive_peak_detection(self, sample_ppg_signal, sampling_rate):
        """Test adaptive peak detection."""
        peaks, values = adaptive_peak_detection(sample_ppg_signal, sampling_rate)
        
        assert len(peaks) == len(values)
        assert np.all(peaks >= 0)
        assert np.all(peaks < len(sample_ppg_signal))
    
    def test_template_matching_peaks(self, sample_ppg_signal, sampling_rate):
        """Test template matching peak detection."""
        peaks, values = template_matching_peaks(sample_ppg_signal, sampling_rate)
        
        assert len(peaks) == len(values)
        assert np.all(peaks >= 0)
        assert np.all(peaks < len(sample_ppg_signal))
    
    def test_validate_peak_detection(self, known_peaks, sample_ppg_signal, sampling_rate):
        """Test peak detection validation."""
        validation = validate_peak_detection(known_peaks, sample_ppg_signal, sampling_rate)
        
        assert isinstance(validation, dict)
        assert 'num_peaks' in validation
        assert 'mean_hr_bpm' in validation
        assert 'detection_quality' in validation
        
        # Check quality assessment
        assert validation['detection_quality'] in ['good', 'fair', 'poor', 'insufficient_peaks', 'no_valid_hr']


class TestHRCalculation:
    """Test cases for heart rate calculation functions."""
    
    def test_calculate_instantaneous_hr_basic(self, known_peaks, sampling_rate):
        """Test basic instantaneous HR calculation."""
        time_points, hr_values = calculate_instantaneous_hr(known_peaks, sampling_rate)
        
        assert len(time_points) == len(hr_values)
        assert len(hr_values) == len(known_peaks) - 1  # One less than peaks
        assert np.all(hr_values > 0), "HR values should be positive"
        assert np.all(hr_values >= 30), "HR should be >= 30 BPM"
        assert np.all(hr_values <= 200), "HR should be <= 200 BPM"
    
    def test_calculate_instantaneous_hr_insufficient_peaks(self, sampling_rate):
        """Test HR calculation with insufficient peaks."""
        single_peak = np.array([100])
        
        with pytest.raises(ValueError, match="Need at least 2 peaks"):
            calculate_instantaneous_hr(single_peak, sampling_rate)
    
    def test_calculate_instantaneous_hr_interpolation(self, known_peaks, sampling_rate):
        """Test HR calculation with interpolation."""
        time_points, hr_values = calculate_instantaneous_hr(
            known_peaks, 
            sampling_rate,
            interpolation_rate=2.0,
            method='linear'
        )
        
        assert len(time_points) == len(hr_values)
        assert np.all(np.diff(time_points) > 0), "Time points should be increasing"
        
        # Check interpolation rate
        expected_dt = 1.0 / 2.0  # 2 Hz
        actual_dt = np.mean(np.diff(time_points))
        assert abs(actual_dt - expected_dt) < 0.1, "Interpolation rate not as expected"
    
    @pytest.mark.parametrize("method", ['linear', 'cubic', 'nearest'])
    def test_calculate_instantaneous_hr_interpolation_methods(self, known_peaks, sampling_rate, method):
        """Test different interpolation methods."""
        time_points, hr_values = calculate_instantaneous_hr(
            known_peaks,
            sampling_rate,
            interpolation_rate=1.0,
            method=method
        )
        
        assert len(time_points) == len(hr_values)
        assert np.all(np.isfinite(hr_values))
    
    def test_calculate_hr_statistics(self):
        """Test HR statistics calculation."""
        # Create test HR data
        hr_values = np.array([70, 72, 68, 75, 73, 71, 69, 74])
        time_points = np.arange(len(hr_values))
        
        stats = calculate_hr_statistics(hr_values, time_points)
        
        assert isinstance(stats, dict)
        assert 'mean_hr_bpm' in stats
        assert 'std_hr_bpm' in stats
        assert 'min_hr_bpm' in stats
        assert 'max_hr_bpm' in stats
        
        # Check calculated values
        assert abs(stats['mean_hr_bpm'] - np.mean(hr_values)) < 1e-6
        assert abs(stats['std_hr_bpm'] - np.std(hr_values)) < 1e-6
        assert stats['min_hr_bpm'] == np.min(hr_values)
        assert stats['max_hr_bpm'] == np.max(hr_values)
    
    def test_calculate_hr_statistics_empty(self):
        """Test HR statistics with empty data."""
        stats = calculate_hr_statistics(np.array([]))
        assert stats == {}
    
    def test_smooth_hr_signal(self):
        """Test HR signal smoothing."""
        # Create noisy HR signal
        t = np.linspace(0, 30, 100)
        hr_clean = 70 + 5 * np.sin(2 * np.pi * 0.1 * t)  # Slow variation
        hr_noisy = hr_clean + 2 * np.random.randn(len(t))  # Add noise
        
        hr_smoothed = smooth_hr_signal(hr_noisy, t, smoothing_window=5.0)
        
        assert len(hr_smoothed) == len(hr_noisy)
        assert np.all(np.isfinite(hr_smoothed))
        
        # Smoothed signal should have less variance
        assert np.var(hr_smoothed) < np.var(hr_noisy)
    
    @pytest.mark.parametrize("method", ['moving_average', 'gaussian', 'median'])
    def test_smooth_hr_signal_methods(self, method):
        """Test different smoothing methods."""
        t = np.linspace(0, 10, 50)
        hr_values = 70 + np.random.randn(len(t))
        
        hr_smoothed = smooth_hr_signal(hr_values, t, method=method)
        
        assert len(hr_smoothed) == len(hr_values)
        assert np.all(np.isfinite(hr_smoothed))
    
    def test_detect_hr_anomalies(self):
        """Test HR anomaly detection."""
        # Create HR signal with anomalies
        t = np.linspace(0, 60, 200)
        hr_normal = 70 + 2 * np.sin(2 * np.pi * 0.05 * t)
        
        # Add anomalies
        hr_with_anomalies = hr_normal.copy()
        hr_with_anomalies[50:70] = 120  # High HR anomaly
        hr_with_anomalies[120:140] = 40  # Low HR anomaly
        
        anomaly_info = detect_hr_anomalies(hr_with_anomalies, t)
        
        assert isinstance(anomaly_info, dict)
        assert 'anomalies' in anomaly_info
        assert 'num_anomalies' in anomaly_info
        assert anomaly_info['num_anomalies'] > 0
    
    def test_calculate_hrv_frequency_domain(self):
        """Test HRV frequency domain analysis."""
        # Create HR signal with sufficient length
        t = np.linspace(0, 300, 1000)  # 5 minutes at high resolution
        hr_values = 70 + 5 * np.sin(2 * np.pi * 0.1 * t) + 2 * np.random.randn(len(t))
        
        hrv_metrics = calculate_hrv_frequency_domain(hr_values, t)
        
        if hrv_metrics:  # May be empty if insufficient data
            assert 'lf_power' in hrv_metrics
            assert 'hf_power' in hrv_metrics
            assert 'lf_hf_ratio' in hrv_metrics
            assert all(v >= 0 for v in hrv_metrics.values())


class TestHREstimationPipeline:
    """Test cases for the complete HR estimation pipeline."""
    
    def test_estimate_heart_rate_basic(self, sample_ppg_signal, sampling_rate):
        """Test basic heart rate estimation pipeline."""
        results = estimate_heart_rate(sample_ppg_signal, sampling_rate)
        
        assert isinstance(results, dict)
        assert results['success'] is True
        assert 'time_points' in results
        assert 'hr_values' in results
        assert 'statistics' in results
        
        # Check HR values are reasonable
        hr_values = results['hr_values']
        assert len(hr_values) > 0
        assert np.all(hr_values >= 30)
        assert np.all(hr_values <= 200)
    
    def test_estimate_heart_rate_with_peaks(self, sample_ppg_signal, sampling_rate):
        """Test HR estimation with peak information."""
        results = estimate_heart_rate(
            sample_ppg_signal, 
            sampling_rate,
            return_peaks=True
        )
        
        assert 'peaks' in results
        assert 'indices' in results['peaks']
        assert 'validation' in results['peaks']
    
    def test_estimate_heart_rate_no_statistics(self, sample_ppg_signal, sampling_rate):
        """Test HR estimation without statistics."""
        results = estimate_heart_rate(
            sample_ppg_signal,
            sampling_rate,
            return_statistics=False
        )
        
        assert 'statistics' not in results
        assert 'anomalies' not in results
        assert 'quality' not in results
    
    def test_estimate_heart_rate_with_smoothing(self, sample_ppg_signal, sampling_rate):
        """Test HR estimation with smoothing."""
        results = estimate_heart_rate(
            sample_ppg_signal,
            sampling_rate,
            smoothing_window=3.0
        )
        
        assert 'hr_values_smoothed' in results
        hr_smoothed = results['hr_values_smoothed']
        hr_original = results['hr_values']
        
        # Smoothed signal should have less variance
        if len(hr_smoothed) > 1 and len(hr_original) > 1:
            assert np.var(hr_smoothed) <= np.var(hr_original)
    
    @pytest.mark.parametrize("method", ['sliding_window', 'adaptive', 'template'])
    def test_estimate_heart_rate_methods(self, sample_ppg_signal, sampling_rate, method):
        """Test HR estimation with different peak detection methods."""
        results = estimate_heart_rate(
            sample_ppg_signal,
            sampling_rate,
            peak_detection_method=method
        )
        
        assert results['success'] is True
        assert results['estimation_parameters']['peak_detection_method'] == method
    
    def test_estimate_heart_rate_insufficient_data(self, sampling_rate):
        """Test HR estimation with insufficient data."""
        short_signal = np.array([1, 2, 3, 4, 5])  # Very short signal
        
        results = estimate_heart_rate(short_signal, sampling_rate)
        
        assert results['success'] is False
        assert 'error' in results
    
    def test_estimate_heart_rate_realtime(self, sampling_rate):
        """Test real-time HR estimation."""
        # Create a buffer with sufficient data
        buffer_length = 15.0  # seconds
        buffer_samples = int(buffer_length * sampling_rate)
        
        # Generate synthetic PPG buffer
        t = np.linspace(0, buffer_length, buffer_samples)
        hr_freq = 75 / 60  # 75 BPM
        ppg_buffer = np.sin(2 * np.pi * hr_freq * t) + 0.3 * np.sin(4 * np.pi * hr_freq * t)
        ppg_buffer += 0.05 * np.random.randn(len(t))
        
        hr_estimate = estimate_heart_rate_realtime(ppg_buffer, sampling_rate)
        
        if hr_estimate is not None:
            assert 30 <= hr_estimate <= 200
    
    def test_assess_hr_quality(self, sample_ppg_signal, sampling_rate):
        """Test HR quality assessment."""
        # First estimate HR
        results = estimate_heart_rate(sample_ppg_signal, sampling_rate)
        
        if results['success']:
            hr_values = results['hr_values']
            time_points = results['time_points']
            
            quality = assess_hr_quality(hr_values, time_points, sample_ppg_signal, sampling_rate)
            
            assert isinstance(quality, dict)
            assert 'overall_quality_score' in quality
            assert 'quality_classification' in quality
            assert 0 <= quality['overall_quality_score'] <= 1
            assert quality['quality_classification'] in ['excellent', 'good', 'fair', 'poor']
