"""
Tests for preprocessing module.

This module contains comprehensive tests for all preprocessing functions
including detrending, denoising, and motion artifact removal.
"""

import pytest
import numpy as np
from scipy import signal

from bspml.preprocessing import (
    wavelet_detrend, 
    adaptive_wavelet_detrend,
    bandpass_filter,
    adaptive_bandpass_filter,
    notch_filter,
    rls_filter,
    nlms_filter,
    preprocess_ppg,
    get_default_preprocessing_params
)
from bspml.preprocessing.motion_artifacts import RLSFilter


class TestDetrending:
    """Test cases for detrending functions."""
    
    def test_wavelet_detrend_basic(self, sample_ppg_signal, sampling_rate):
        """Test basic wavelet detrending functionality."""
        detrended = wavelet_detrend(sample_ppg_signal)
        
        # Check output properties
        assert len(detrended) == len(sample_ppg_signal)
        assert np.isfinite(detrended).all()
        
        # Check that low-frequency content is reduced
        freqs_orig, psd_orig = signal.welch(sample_ppg_signal, sampling_rate)
        freqs_det, psd_det = signal.welch(detrended, sampling_rate)
        
        # Low frequency power should be reduced
        lf_mask = freqs_orig < 0.5
        lf_power_orig = np.sum(psd_orig[lf_mask])
        lf_power_det = np.sum(psd_det[lf_mask])
        
        assert lf_power_det < lf_power_orig
    
    def test_wavelet_detrend_empty_signal(self):
        """Test wavelet detrending with empty signal."""
        with pytest.raises(ValueError, match="Input signal cannot be empty"):
            wavelet_detrend(np.array([]))
    
    def test_wavelet_detrend_invalid_values(self):
        """Test wavelet detrending with invalid values."""
        signal_with_nan = np.array([1, 2, np.nan, 4, 5])
        with pytest.raises(ValueError, match="Input signal contains non-finite values"):
            wavelet_detrend(signal_with_nan)
    
    @pytest.mark.parametrize("wavelet", ['db4', 'db8', 'haar', 'coif2'])
    def test_wavelet_detrend_different_wavelets(self, sample_ppg_signal, wavelet):
        """Test detrending with different wavelet types."""
        detrended = wavelet_detrend(sample_ppg_signal, wavelet=wavelet)
        
        assert len(detrended) == len(sample_ppg_signal)
        assert np.isfinite(detrended).all()
    
    def test_adaptive_wavelet_detrend(self, sample_ppg_signal, sampling_rate):
        """Test adaptive wavelet detrending."""
        detrended = adaptive_wavelet_detrend(
            sample_ppg_signal, 
            sampling_rate, 
            cutoff_freq=0.1
        )
        
        assert len(detrended) == len(sample_ppg_signal)
        assert np.isfinite(detrended).all()


class TestDenoising:
    """Test cases for denoising functions."""
    
    def test_bandpass_filter_basic(self, sample_ppg_signal, sampling_rate):
        """Test basic bandpass filtering."""
        filtered = bandpass_filter(sample_ppg_signal, sampling_rate)
        
        assert len(filtered) == len(sample_ppg_signal)
        assert np.isfinite(filtered).all()
        
        # Check frequency response
        freqs, psd = signal.welch(filtered, sampling_rate)
        
        # Power should be concentrated in passband (0.5-4 Hz)
        passband_mask = (freqs >= 0.5) & (freqs <= 4.0)
        stopband_mask = (freqs < 0.3) | (freqs > 5.0)
        
        passband_power = np.sum(psd[passband_mask])
        stopband_power = np.sum(psd[stopband_mask])
        
        # Passband should have more power than stopband
        assert passband_power > stopband_power
    
    def test_bandpass_filter_invalid_cutoffs(self, sample_ppg_signal, sampling_rate):
        """Test bandpass filter with invalid cutoff frequencies."""
        # Low cutoff >= high cutoff
        with pytest.raises(ValueError, match="Low cutoff must be less than high cutoff"):
            bandpass_filter(sample_ppg_signal, sampling_rate, low_cutoff=2.0, high_cutoff=1.0)
        
        # High cutoff >= Nyquist frequency
        with pytest.raises(ValueError, match="High cutoff must be less than Nyquist frequency"):
            bandpass_filter(sample_ppg_signal, sampling_rate, high_cutoff=sampling_rate/2)
    
    @pytest.mark.parametrize("filter_type", ['butterworth', 'chebyshev1', 'elliptic'])
    def test_bandpass_filter_types(self, sample_ppg_signal, sampling_rate, filter_type):
        """Test different filter types."""
        filtered = bandpass_filter(
            sample_ppg_signal, 
            sampling_rate, 
            filter_type=filter_type
        )
        
        assert len(filtered) == len(sample_ppg_signal)
        assert np.isfinite(filtered).all()
    
    def test_adaptive_bandpass_filter(self, sample_ppg_signal, sampling_rate):
        """Test adaptive bandpass filtering."""
        # Test with estimated HR
        filtered = adaptive_bandpass_filter(
            sample_ppg_signal, 
            sampling_rate, 
            estimated_hr=75.0
        )
        
        assert len(filtered) == len(sample_ppg_signal)
        assert np.isfinite(filtered).all()
        
        # Test without estimated HR
        filtered_no_hr = adaptive_bandpass_filter(sample_ppg_signal, sampling_rate)
        
        assert len(filtered_no_hr) == len(sample_ppg_signal)
        assert np.isfinite(filtered_no_hr).all()
    
    def test_notch_filter(self, sample_ppg_signal, sampling_rate):
        """Test notch filtering."""
        # Add 50 Hz interference
        t = np.arange(len(sample_ppg_signal)) / sampling_rate
        interference = 0.1 * np.sin(2 * np.pi * 50 * t)
        signal_with_interference = sample_ppg_signal + interference
        
        # Apply notch filter
        filtered = notch_filter(signal_with_interference, sampling_rate, notch_freq=50.0)
        
        assert len(filtered) == len(signal_with_interference)
        assert np.isfinite(filtered).all()
        
        # Check that 50 Hz component is reduced
        freqs, psd_orig = signal.welch(signal_with_interference, sampling_rate)
        freqs, psd_filt = signal.welch(filtered, sampling_rate)
        
        # Find 50 Hz bin
        freq_50_idx = np.argmin(np.abs(freqs - 50.0))
        
        # Power at 50 Hz should be reduced
        assert psd_filt[freq_50_idx] < psd_orig[freq_50_idx]


class TestMotionArtifactRemoval:
    """Test cases for motion artifact removal functions."""
    
    def test_rls_filter_basic(self, sample_ppg_signal, sample_acc_signals):
        """Test basic RLS filtering."""
        filtered = rls_filter(sample_ppg_signal, sample_acc_signals)
        
        assert len(filtered) == len(sample_ppg_signal)
        assert np.isfinite(filtered).all()
    
    def test_rls_filter_invalid_inputs(self, sample_ppg_signal):
        """Test RLS filter with invalid inputs."""
        # Empty PPG signal
        with pytest.raises(ValueError, match="PPG signal cannot be empty"):
            rls_filter(np.array([]), np.array([[1, 2, 3]]))
        
        # Wrong ACC dimensions
        with pytest.raises(ValueError, match="Accelerometer signals must be 2D array"):
            rls_filter(sample_ppg_signal, np.array([1, 2, 3]))
        
        # Mismatched lengths
        short_acc = np.array([[1, 2, 3], [4, 5, 6]])
        with pytest.raises(ValueError, match="PPG and accelerometer signals must have same length"):
            rls_filter(sample_ppg_signal, short_acc)
    
    def test_rls_filter_class(self):
        """Test RLS filter class."""
        rls = RLSFilter(filter_order=4, forgetting_factor=0.95)
        
        # Test single update
        ref_vector = np.array([0.1, 0.2, 0.3, 0.4])
        desired = 1.0
        
        output = rls.update(ref_vector, desired)
        assert np.isfinite(output)
        
        # Test reset
        rls.reset()
        assert np.allclose(rls.weights, 0)
    
    def test_nlms_filter(self, sample_ppg_signal, sample_acc_signals):
        """Test NLMS filtering."""
        filtered = nlms_filter(sample_ppg_signal, sample_acc_signals)
        
        assert len(filtered) == len(sample_ppg_signal)
        assert np.isfinite(filtered).all()
    
    @pytest.mark.parametrize("filter_order", [4, 8, 16])
    def test_rls_filter_orders(self, sample_ppg_signal, sample_acc_signals, filter_order):
        """Test RLS filter with different orders."""
        filtered = rls_filter(
            sample_ppg_signal, 
            sample_acc_signals, 
            filter_order=filter_order
        )
        
        assert len(filtered) == len(sample_ppg_signal)
        assert np.isfinite(filtered).all()


class TestPreprocessingPipeline:
    """Test cases for the complete preprocessing pipeline."""
    
    def test_preprocess_ppg_basic(self, ppg_with_artifacts, sample_acc_signals, sampling_rate):
        """Test basic preprocessing pipeline."""
        processed = preprocess_ppg(
            ppg_with_artifacts, 
            sample_acc_signals, 
            sampling_rate
        )
        
        assert len(processed) == len(ppg_with_artifacts)
        assert np.isfinite(processed).all()
    
    def test_preprocess_ppg_no_acc(self, ppg_with_artifacts, sampling_rate):
        """Test preprocessing without accelerometer data."""
        processed = preprocess_ppg(
            ppg_with_artifacts, 
            acc_signals=None, 
            sampling_rate=sampling_rate,
            enable_motion_removal=False
        )
        
        assert len(processed) == len(ppg_with_artifacts)
        assert np.isfinite(processed).all()
    
    def test_preprocess_ppg_selective_processing(self, ppg_with_artifacts, sampling_rate):
        """Test selective enabling/disabling of processing steps."""
        # Only detrending
        processed_detrend = preprocess_ppg(
            ppg_with_artifacts,
            sampling_rate=sampling_rate,
            enable_detrending=True,
            enable_denoising=False,
            enable_motion_removal=False
        )
        
        # Only denoising
        processed_denoise = preprocess_ppg(
            ppg_with_artifacts,
            sampling_rate=sampling_rate,
            enable_detrending=False,
            enable_denoising=True,
            enable_motion_removal=False
        )
        
        assert len(processed_detrend) == len(ppg_with_artifacts)
        assert len(processed_denoise) == len(ppg_with_artifacts)
        assert np.isfinite(processed_detrend).all()
        assert np.isfinite(processed_denoise).all()
    
    def test_preprocess_ppg_with_quality_assessment(self, ppg_with_artifacts, sample_acc_signals, sampling_rate):
        """Test preprocessing with quality assessment."""
        from bspml.preprocessing.pipeline import preprocess_ppg_with_quality_assessment
        
        processed, quality = preprocess_ppg_with_quality_assessment(
            ppg_with_artifacts,
            sample_acc_signals,
            sampling_rate
        )
        
        assert len(processed) == len(ppg_with_artifacts)
        assert isinstance(quality, dict)
        assert 'signal_correlation' in quality
        assert 'snr_improvement_db' in quality
    
    def test_get_default_preprocessing_params(self):
        """Test default parameter retrieval."""
        params = get_default_preprocessing_params()
        
        assert isinstance(params, dict)
        assert 'wavelet' in params
        assert 'bandpass' in params
        assert 'rls' in params
        
        # Check parameter structure
        assert 'wavelet' in params['wavelet']
        assert 'low_cutoff' in params['bandpass']
        assert 'filter_order' in params['rls']
    
    @pytest.mark.parametrize("method_combo", [
        ('wavelet', 'bandpass', 'rls'),
        ('adaptive_wavelet', 'adaptive_bandpass', 'nlms'),
        ('wavelet', 'adaptive_bandpass', 'rls')
    ])
    def test_preprocess_ppg_method_combinations(self, ppg_with_artifacts, sample_acc_signals, sampling_rate, method_combo):
        """Test different combinations of preprocessing methods."""
        detrend_method, denoise_method, motion_method = method_combo
        
        processed = preprocess_ppg(
            ppg_with_artifacts,
            sample_acc_signals,
            sampling_rate,
            detrending_method=detrend_method,
            denoising_method=denoise_method,
            motion_removal_method=motion_method
        )
        
        assert len(processed) == len(ppg_with_artifacts)
        assert np.isfinite(processed).all()
    
    def test_preprocess_ppg_invalid_methods(self, sample_ppg_signal, sampling_rate):
        """Test preprocessing with invalid method names."""
        with pytest.raises(ValueError, match="Unknown detrending method"):
            preprocess_ppg(
                sample_ppg_signal,
                sampling_rate=sampling_rate,
                detrending_method='invalid_method'
            )
        
        with pytest.raises(ValueError, match="Unknown denoising method"):
            preprocess_ppg(
                sample_ppg_signal,
                sampling_rate=sampling_rate,
                denoising_method='invalid_method'
            )
        
        with pytest.raises(ValueError, match="Unknown motion removal method"):
            preprocess_ppg(
                sample_ppg_signal,
                acc_signals=np.array([[1, 2, 3]]),
                sampling_rate=sampling_rate,
                motion_removal_method='invalid_method'
            )
